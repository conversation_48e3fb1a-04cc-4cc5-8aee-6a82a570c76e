
/**
 * 标准 API 响应结构
 */
export interface ApiResponse<T = any> {
  code: number
  data?: T
  msg?: string
  error?: string
}

/**
 * 通用实体基础接口
 * 所有领域模型的基础类型
 */
export interface BaseEntity {
  id: any
  created_at: number
  updated_at: number
  deleted_at: number
}

/**
 * 通用时间戳字段接口
 */
export interface TimeStampFields {
  created_at?: number
  updated_at?: number
  deleted_at?: number
}

/**
 * 通用所有权字段接口
 */
export interface OwnershipFields {
  uid: string
  team_id?: number | null
}

/**
 * 通用分页查询结果
 */
export interface PaginatedResult<T> {
  list: T[]
  total: number
}

export interface QueryCommonParams {
  sortBy?: string
  order?: 'asc' | 'desc'
}

/**
 * 通用分页查询参数
 */
export interface PaginationParams extends QueryCommonParams {
  pageNo?: number
  pageSize?: number
}

/**
 * 通用查询条件类型
 */
export type QueryConditions = Record<string, any>

/**
 * 通用排序选项
 */
export interface SortOptions {
  sortBy: string
  order: 'asc' | 'desc'
}

/**
 * 通用响应状态
 */
export enum ResponseStatus {
  SUCCESS = 'success',
  ERROR = 'error',
}

/**
 * 文件夹相关类型
 */
export namespace Folder {
  /**
     * 文件夹基础接口
     */
  export interface IFolder extends BaseEntity, OwnershipFields {
    parent_id: number
    material_type: number
    path: string
    title: string
    auto_create: number
  }

  /**
 * 创建文件夹参数
 */
  export interface CreateParams extends OwnershipFields {
    parent_id?: number
    material_type?: number
    path?: string
    title: string
    auto_create?: number
  }

  /**
 * 更新文件夹参数
 */
  export interface UpdateParams extends TimeStampFields {
    parent_id?: number
    path?: string
    title?: string
    team_id?: number | null
    auto_create?: number
  }

  /**
 * 查询文件夹参数
 */
  export interface QueryParams extends Partial<OwnershipFields> {
    parent_id?: number
    deleted_at?: number
    material_type?: number
  }

  /**
     * 文件夹路径查询结果
     */
  export interface StatsResult {
    path: IFolder[]
    current: IFolder | null
  }
}

/**
 * 素材文件相关类型
 */
export namespace MaterialFile {
  /**
     * 素材文件类型枚举
     */
  export enum Type {
    VIDEO = 1,
    AUDIO = 2,
    IMAGE = 3,
  }

  /**
     * 素材文件状态枚举
     */
  export enum Status {
    UNPROCESSED = 0,
    PROCESSING = 1,
    COMPLETED = 2,
    FAILED = 3,
  }

  /**
     * 素材存储位置枚举
     */
  export enum StorageLocation {
    INITIAL = 0,
    CLOUD = 1,
    LOCAL = 2,
  }

  /**
     * 素材文件基础接口
     */
  export interface IMaterialFile extends BaseEntity, OwnershipFields {
    id: string
    parent_id: number
    material_type: number
    path: string
    source_path: string
    size: number
    duration: number
    width: number
    height: number
    codec_name: string
    codec_type: string
    bit_rate: number | null
    nb_frames: number | null
    cover: string
    hash: string
    status: number
    clip_cloud_or_local: number
    object_oid: string
    reason: string
    title: string
    upload_id: number
    task_no: string | null
    clipinfo: string
    tag_id: number
  }

  /**
     * 创建素材文件参数
     */
  export interface CreateParams extends OwnershipFields {
    id: string
    parent_id?: number
    material_type?: number
    path?: string
    source_path?: string
    size?: number
    duration?: number
    width?: number
    height?: number
    codec_name?: string
    codec_type?: string
    bit_rate?: number | null
    nb_frames?: number | null
    cover?: string
    hash?: string
    status?: number
    clip_cloud_or_local?: number
    object_oid?: string
    reason?: string
    title: string
    upload_id?: number
    task_no?: string | null
    clipinfo?: string
    tag_id?: number
  }

  /**
     * 更新素材文件参数
 */
  export interface UpdateParams extends TimeStampFields {
    parent_id?: number
    team_id?: number | null
    path?: string
    source_path?: string
    size?: number
    duration?: number
    width?: number
    height?: number
    codec_name?: string
    codec_type?: string
    bit_rate?: number | null
    nb_frames?: number | null
    cover?: string
    hash?: string
    status?: number
    clip_cloud_or_local?: number
    object_oid?: string
    reason?: string
    title?: string
    upload_id?: number
    task_no?: string | null
    clipinfo?: string
    tag_id?: number
  }

  /**
     * 查询素材文件参数
     */
  export interface QueryParams extends Partial<OwnershipFields> {
    id?: string
    parent_id?: number
    material_type?: number
    status?: number
    clip_cloud_or_local?: number
    deleted_at?: number
    tag_id?: number
  }

  /**
     * 素材文件搜索参数
     */
  export interface SearchParams extends Partial<OwnershipFields> {
    keyword: string
    types?: number[]
    startDate?: number
    endDate?: number
  }

  /**
     * 素材文件统计结果
     */
  export interface StatsResult {
    totalCount: number
    totalSize: number
    typeDistribution: Record<number, number>
  }
}

