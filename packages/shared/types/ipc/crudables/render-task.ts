import { CrudableIpcClient } from '../../crudable-ipc-client.js'
import { RenderTask } from '../../render-task.types.js'

export interface RenderTaskIPCClientUniqueUtilities {
  /**
   * 获取用户渲染任务列表
   */
  getUserTasks(params: {
    uid: string
    teamId?: number | null
    status?: RenderTask.RenderTaskStatus
  }): Promise<RenderTask.IRenderTask[]>

  /**
   * 获取任务统计信息
   */
  getTaskStats(params: {
    uid: string
    teamId?: number | null
  }): Promise<RenderTask.TaskStats>

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(params: {
    uid: string
    teamId?: number | null
  }): Promise<number>
}

/**
 * 渲染任务IPC客户端接口
 */
export interface RenderTaskIPCClient extends CrudableIpcClient<
  RenderTask.IRenderTask,
  RenderTask.CreateParams,
  RenderTask.QueryParams,
  RenderTask.UpdateParams,
  any
>, RenderTaskIPCClientUniqueUtilities {
}
