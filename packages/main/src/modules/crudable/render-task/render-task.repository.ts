import { Inject, Injectable } from '@nestjs/common'
import { RenderTaskModel } from '@/infra/models/RenderTaskModel.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'
import { RenderTask } from '@app/shared/types/render-task.types.js'

/**
 * 渲染任务仓储类
 */
@Injectable()
export class RenderTaskRepository extends BaseRepository<
  RenderTaskModel,
  RenderTask.CreateParams,
  RenderTask.UpdateParams,
  RenderTask.QueryParams
> {

  /**
   * 表名
   */
  protected readonly tableName = 'render_task'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'name', 'status', 'progress', 'file_size',
    'updated_at', 'created_at', 'render_start_time', 'render_end_time'
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'team_id', 'uid', 'status', 'script_id', 'task_no', 'deleted_at'
  ]

  constructor(
    @Inject(NestDatabaseService)
    protected readonly databaseService: NestDatabaseService
  ) {
    super(databaseService)
  }

  /**
   * 获取用户渲染任务
   */
  findUserTasks(uid: string, status?: RenderTask.RenderTaskStatus, teamId?: number | null): RenderTaskModel[] {
    return super.queryAll({
      uid,
      status,
      team_id: teamId
    })
  }

  /**
   * 根据任务编号查找任务
   */
  findByTaskNo(taskNo: string): RenderTaskModel | null {
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE task_no = ? AND deleted_at = 0
      LIMIT 1
    `
    const row = this.db.prepare(sql).get(taskNo) as Record<string, any> | undefined
    return row ? this.toModel(row) : null
  }

  /**
   * 根据任务编号批量查找任务
   */
  findByTaskNos(taskNos: string[]): RenderTaskModel[] {
    if (taskNos.length === 0) return []

    const placeholders = taskNos.map(() => '?').join(',')
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE task_no IN (${placeholders}) AND deleted_at = 0
      ORDER BY created_at DESC
    `
    const rows = this.db.prepare(sql).all(...taskNos) as Record<string, any>[]
    return rows.map(row => this.toModel(row)).filter((model): model is RenderTaskModel => model !== null)
  }

  /**
   * 获取需要监听的任务（未完成的任务）
   */
  findPendingTasks(): RenderTaskModel[] {
    const sql = `
      SELECT * FROM ${this.tableName}
      WHERE status not IN (?, ?, ?) AND deleted_at = 0
      ORDER BY created_at DESC
    `
    const rows = this.db.prepare(sql).all(
      RenderTask.RenderTaskStatus.COMPLETED,
      RenderTask.RenderTaskStatus.FAILED,
      RenderTask.RenderTaskStatus.ASSIGN_FAILED,
    ) as Record<string, any>[]
    return rows.map(row => this.toModel(row)).filter((model): model is RenderTaskModel => model !== null)
  }

  /**
   * 获取任务统计信息
   */
  getTaskStats(uid: string, teamId?: number | null): RenderTask.TaskStats {
    let sql = `
      SELECT
        COUNT(*) as total_count,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as rendering_count,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as completed_count,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed_count,
        SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as cancelled_count
      FROM ${this.tableName}
      WHERE uid = ? AND deleted_at = 0
    `
    const params: any[] = [
      RenderTask.RenderTaskStatus.WAITING,
      RenderTask.RenderTaskStatus.RENDERING,
      RenderTask.RenderTaskStatus.COMPLETED,
      RenderTask.RenderTaskStatus.FAILED,
      RenderTask.RenderTaskStatus.CANCELED,
      uid
    ]

    if (teamId !== undefined && teamId !== null) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    const result = this.db.prepare(sql).get(...params) as any
    return {
      total_count: result.total_count || 0,
      pending_count: result.pending_count || 0,
      rendering_count: result.rendering_count || 0,
      completed_count: result.completed_count || 0,
      failed_count: result.failed_count || 0,
      cancelled_count: result.cancelled_count || 0
    }
  }

  /**
   * 清理已完成的任务
   */
  cleanupCompleted(uid: string, teamId?: number | null): number {
    let sql = `
      UPDATE ${this.tableName}
      SET deleted_at = ?, updated_at = ?
      WHERE uid = ? AND status = ? AND deleted_at = 0
    `
    const params: any[] = [Date.now(), Date.now(), uid, RenderTask.RenderTaskStatus.COMPLETED]

    if (teamId !== undefined && teamId !== null) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    const result = this.db.prepare(sql).run(...params)
    return result.changes
  }

  /**
   * 将数据库行转换为模型
   */
  protected toModel(row: Record<string, any>): RenderTaskModel | null {
    try {
      return new RenderTaskModel(row)
    } catch (error) {
      console.error('转换渲染任务模型失败:', error)
      return null
    }
  }
}
