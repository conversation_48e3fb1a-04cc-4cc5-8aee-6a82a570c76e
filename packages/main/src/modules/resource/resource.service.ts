import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { checkFileExists, ensureCacheDirExists, getLocalFilePath } from '@/infra/utils/fs.js'
import { ResourceCacheEntry, ResourceCacheType } from '@app/shared/types/resource-cache.types.js'
import fetch from 'node-fetch'
import { promises as fsPromises } from 'fs'
import path from 'path'
import { ResourceIPCClient } from '@app/shared/types/ipc/resource.js'
import { RequestService } from '../global/request.service.js'
import { hashUrl, isCacheEntryExpired, ManifestManager } from './resource.utils.js'

@Injectable()
export class ResourceService implements OnModuleInit, ResourceIPCClient {

  private readonly logger = new Logger(ResourceService.name)

  // 资源缓存配置
  private readonly defaultMaxAgeSeconds: number
  private readonly defaultMaxCacheSizeMB: number

  // 用于缓存文件存在性检查结果的内存缓存
  private fileExistsCache: Map<string, { exists: boolean, timestamp: number }>

  constructor(
    @Inject(RequestService) private readonly requestService: RequestService
  ) {
    this.defaultMaxAgeSeconds = 7 * 24 * 60 * 60 // 7 天
    this.defaultMaxCacheSizeMB = 1024 // 1 GB
    this.fileExistsCache = new Map()
  }

  /**
   * 初始化资源缓存服务
   */
  async onModuleInit() {
    try {
      // 初始化资源缓存系统
      await ManifestManager.initManifestManager()

      // 执行文件完整性检查，确保 manifest 与实际文件系统状态一致
      await this.validateCacheIntegrity()

      // 启动时执行一次缓存清理
      this.cleanResource().catch(err => {
        this.logger.error('[ResourceService] 初始化缓存清理失败:', err)
      })
    } catch (error) {
      this.logger.error('[ResourceService] 初始化失败:', error)
      throw error
    }
  }

  async fetchOrSaveResource(props: {
    url: string;
    type: ResourceCacheType;
    version?: string;
    customExt?: string
  }): Promise<string> {
    const { url, type, version = '1.0.0', customExt } = props

    if (!url || !type) {
      throw new Error('URL和资源类型不能为空')
    }

    const key = hashUrl(url)
    const localPath = getLocalFilePath(type, key, customExt)

    const entry = ManifestManager.getEntry(key)

    if (entry) {
      // 更新访问时间
      entry.lastAccessed = Math.floor(Date.now() / 1000)
      await ManifestManager.setEntry(entry)

      if (entry.version === version) {
        const fileExists = await this.checkFileExistsWithCache(entry.localPath)
        if (fileExists) {
          // console.log(`Resource already cached and up-to-date: ${url}`)
          return entry.localPath
        } else {
          // 文件不存在但 manifest 中有记录，清理孤儿条目
          this.logger.warn(`Resource file missing, cleaning orphaned entry: ${url} -> ${entry.localPath}`)
          await ManifestManager.removeEntry(key)
        }
      } else {
        this.logger.log(`Resource version mismatch, updating: ${url}`)
        // 版本不一致，需要重新下载
        await ManifestManager.removeEntry(key) // 移除旧的缓存条目
      }
    }

    // 下载资源
    const { etag, size } = await this.downloadResource(url, localPath, type)

    // 字体文件直接使用下载的路径
    const finalLocalPath = localPath

    // 创建或更新缓存条目
    const newEntry: ResourceCacheEntry = {
      key,
      url,
      type,
      localPath: finalLocalPath, // 使用可能更新过的路径
      version,
      etag,
      lastAccessed: Math.floor(Date.now() / 1000),
      downloadedAt: Math.floor(Date.now() / 1000),
      size,
    }
    await ManifestManager.setEntry(newEntry)

    // 更新文件存在性缓存
    this.fileExistsCache.set(finalLocalPath, { exists: true, timestamp: Date.now() })

    return finalLocalPath
  }

  async cleanResource(props?: { maxAgeSeconds?: number; maxCacheSizeMB?: number }): Promise<void> {
    const {
      maxAgeSeconds = this.defaultMaxAgeSeconds,
      maxCacheSizeMB = this.defaultMaxCacheSizeMB
    } = props || {}

    // console.log('开始清理缓存...')
    const allEntries = ManifestManager.listAllEntries()
    const entriesToDelete: ResourceCacheEntry[] = []
    let currentTotalSize = 0

    // 清空文件存在性缓存
    this.fileExistsCache.clear()

    for (const entry of allEntries) {
      // 计算当前总大小
      try {
        const fileExists = await checkFileExists(entry.localPath)
        if (fileExists) {
          const stats = await fsPromises.stat(entry.localPath)
          currentTotalSize += stats.size
        } else {
          this.logger.warn(`文件 ${entry.localPath} 不存在，将删除对应 manifest 条目`)
          entriesToDelete.push(entry)
          continue // 跳过此条目，因为它可能已损坏
        }
      } catch (e) {
        this.logger.warn(`无法获取文件 ${entry.localPath} 的大小，可能已损坏或丢失，将删除对应 manifest 条目:`, e)
        entriesToDelete.push(entry)
        continue // 跳过此条目，因为它可能已损坏
      }

      // 检查是否过期
      if (isCacheEntryExpired(entry, maxAgeSeconds)) {
        // console.log(`Deleting expired entry: ${entry.url}`)
        entriesToDelete.push(entry)
      }
    }

    // 如果总大小超出限制，按照 LRU (最近最少使用) 策略删除
    const maxCacheSizeBytes = maxCacheSizeMB * 1024 * 1024
    if (currentTotalSize > maxCacheSizeBytes) {
      this.logger.log(`Cache size (${(currentTotalSize / (1024 * 1024)).toFixed(2)} MB) exceeds limit (${maxCacheSizeMB} MB), applying LRU.`)
      // 按照 lastAccessed 升序排列 (最早访问的在前面)
      const sortedEntries = allEntries.sort((a, b) => a.lastAccessed - b.lastAccessed)

      for (const entry of sortedEntries) {
        if (currentTotalSize <= maxCacheSizeBytes) break // 已达到目标大小
        if (!entriesToDelete.includes(entry)) {
          // console.log(`Deleting LRU entry: ${entry.url}`)
          entriesToDelete.push(entry)
          // 减去文件大小 (假设文件存在)
          try {
            const stats = await fsPromises.stat(entry.localPath)
            currentTotalSize -= stats.size
          } catch (e) {
            this.logger.warn(`无法获取 LRU 文件 ${entry.localPath} 的大小，可能已损坏或丢失:`, e)
          }
        }
      }
    }

    // 执行删除操作
    for (const entry of entriesToDelete) {
      try {
        await fsPromises.unlink(entry.localPath) // 删除文件

        // 字体文件直接删除，无需特殊处理
        await ManifestManager.removeEntry(entry.key) // 从 manifest 中移除
        this.logger.log(`已清理缓存文件: ${entry.localPath}`)
      } catch (error) {
        this.logger.error(`Error deleting cached file ${entry.localPath}:`, error)
      }
    }
  }

  getResource({ url }: { url: string }): Promise<ResourceCacheEntry | undefined> {
    if (!url) {
      throw new Error('URL不能为空')
    }

    const key = hashUrl(url)
    return Promise.resolve(ManifestManager.getEntry(key))
  }

  async getResourcePath({ url, type }: { url: string; type: ResourceCacheType }): Promise<string> {
    if (!url || !type) {
      throw new Error('URL和资源类型不能为空')
    }

    // 优先查找缓存条目，获取可能已经解压的路径
    const key = hashUrl(url)
    const entry = ManifestManager.getEntry(key)

    // 如果找到缓存条目，且类型匹配，直接返回其路径
    if (entry && entry.type === type) {
      // console.log(`[getResourceCachePath] 找到缓存条目: ${entry.localPath}`)
      return entry.localPath
    }

    // 如果没有缓存条目，或类型不匹配，生成默认路径
    const ext = path.extname(url)
    const defaultPath = getLocalFilePath(type, key, ext)

    // 字体文件直接使用默认路径，无需特殊处理
    // console.log(`[getResourceCachePath] 未找到缓存条目，使用默认路径: ${defaultPath}`)
    return defaultPath
  }

  async getAllResources(): Promise<ResourceCacheEntry[]> {
    const allEntries = ManifestManager.listAllEntries()
    const validEntries: ResourceCacheEntry[] = []
    const invalidKeys: string[] = []

    // 检查每个缓存条目对应的文件是否实际存在
    for (const entry of allEntries) {
      try {
        const fileExists = await this.checkFileExistsWithCache(entry.localPath)
        if (fileExists) {
          validEntries.push(entry)
        } else {
          this.logger.warn(`缓存文件不存在，将从manifest中移除: ${entry.localPath}`)
          invalidKeys.push(entry.key)
        }
      } catch (e) {
        this.logger.warn(`检查缓存文件是否存在时出错: ${entry.localPath}`, e)
        invalidKeys.push(entry.key)
      }
    }

    // 批量清理无效条目
    if (invalidKeys.length > 0) {
      await this.cleanInvalidEntries(invalidKeys)
    }

    return validEntries
  }

  /**
   * 批量清理无效的缓存条目
   * @param keys 缓存条目的key数组
   */
  private async cleanInvalidEntries(keys: string[]): Promise<void> {
    try {
      await ManifestManager.batchRemoveEntries(keys)
      this.logger.log(`已批量清理 ${keys.length} 个无效缓存条目`)
    } catch (error) {
      this.logger.error('批量清理无效缓存条目失败，回退到逐个清理:', error)
      // 如果批量清理失败，回退到逐个清理
      for (const key of keys) {
        try {
          await ManifestManager.removeEntry(key)
        } catch (e) {
          this.logger.error(`清理缓存条目失败: ${key}`, e)
        }
      }
      this.logger.log(`已逐个清理 ${keys.length} 个无效缓存条目`)
    }
  }

  /**
   * 带缓存的文件存在性检查，减少文件系统操作
   * @param filePath 文件路径
   * @returns 文件是否存在
   */
  private async checkFileExistsWithCache(filePath: string): Promise<boolean> {
    // 缓存有效期为5分钟
    const CACHE_TTL = 5 * 60 * 1000
    const now = Date.now()

    // 检查缓存
    const cached = this.fileExistsCache.get(filePath)
    if (cached && (now - cached.timestamp) < CACHE_TTL) {
      return cached.exists
    }

    // 缓存不存在或已过期，执行异步检查
    try {
      const exists = await checkFileExists(filePath)
      // 更新缓存
      this.fileExistsCache.set(filePath, { exists, timestamp: now })
      return exists
    } catch (error) {
      this.logger.error(`检查文件是否存在时出错: ${filePath}`, error)
      // 出错时假设文件不存在
      this.fileExistsCache.set(filePath, { exists: false, timestamp: now })
      return false
    }
  }

  /**
   * 下载资源并保存到本地文件
   * @param url 资源 URL
   * @param localPath 本地保存路径
   * @param type 资源类型. 用于确认下载的目标目录
   * @returns 包含 size 的 Promise
   */
  private async downloadResource(url: string, localPath: string, type: ResourceCacheType): Promise<{ etag: string; size: number }> {
    await ensureCacheDirExists(type) // 确保目录存在

    // 确保URL格式正确
    let finalUrl = url
    if (!/^https?:\/\//.test(url)) {
      finalUrl = `https://${url}`
      this.logger.log(`URL格式调整: ${url} -> ${finalUrl}`)
    }

    if (/oss\/object-href\/\w+$/.test(url)) {
      finalUrl = await this.requestService.parseUrlFromObjectHref(url)
    }

    try {
      const response = await fetch(finalUrl)

      if (response.status === 304) {
        const existingEntry = ManifestManager.getEntry(hashUrl(url))
        if (existingEntry) {
          return { etag: existingEntry.etag || '', size: existingEntry.size }
        } else {
          throw new Error('ETag匹配但未找到现有条目.')
        }
      }

      if (!response.ok) {
        this.logger.error(`下载失败: ${finalUrl}, 状态码: ${response.status}, 消息: ${response.statusText}`)
        throw new Error(`下载资源失败: ${response.statusText} (${response.status})`)
      }

      const buffer = await response.buffer()
      await fsPromises.writeFile(localPath, buffer)

      const etag = response.headers.get('etag') || ''
      const contentLength = response.headers.get('content-length')
      const size = contentLength ? parseInt(contentLength, 10) : buffer.length

      // this.logger.log(`缓存[${type}]类型的资源到本地(${url} ===> ${localPath})`)
      return { etag, size }
    } catch (error) {
      this.logger.error(`下载资源时发生错误: ${finalUrl}`, error)
      throw new Error(`下载资源失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 根据本地路径判断资源类型（辅助方法）
   * @param localPath 本地文件路径
   * @returns ResourceCacheType
   */
  private getLocalResourceType(localPath: string): ResourceCacheType {
    // 从路径中提取资源类型，这需要和 getLocalFilePath 的拼接方式对应
    const parts = localPath.split(path.sep)
    const resourceTypeString = parts[parts.length - 2] // 倒数第二个部分是类型文件夹名

    // 验证是否是有效的 ResourceType
    if (Object.values(ResourceCacheType).includes(resourceTypeString as ResourceCacheType)) {
      return resourceTypeString as ResourceCacheType
    }

    throw new Error(`无法从路径 ${localPath} 推断资源类型`)
  }

  /**
   * 验证缓存完整性
   * 检查 manifest.json 中记录的缓存条目是否与实际文件系统状态一致
   * 清理那些文件已被手动删除但仍在 manifest 中记录的孤儿条目
   */
  private async validateCacheIntegrity(): Promise<void> {
    const allEntries = ManifestManager.listAllEntries()
    const invalidEntries: ResourceCacheEntry[] = []
    let validCount = 0
    let invalidCount = 0

    // 清空文件存在性缓存，确保检查的准确性
    this.fileExistsCache.clear()

    // 检查每个缓存条目对应的文件是否实际存在
    for (const entry of allEntries) {
      try {
        const fileExists = await checkFileExists(entry.localPath)
        if (!fileExists) {
          this.logger.warn(`[ResourceService] 发现孤儿缓存条目: ${entry.url} -> ${entry.localPath}`)
          invalidEntries.push(entry)
          invalidCount++
        } else {
          validCount++
        }
      } catch (error) {
        this.logger.error(`[ResourceService] 检查文件存在性时出错: ${entry.localPath}`, error)
        invalidEntries.push(entry)
        invalidCount++
      }
    }

    // 批量清理无效的缓存条目
    if (invalidEntries.length > 0) {
      // this.logger.log(`[ResourceService] 发现 ${invalidEntries.length} 个无效缓存条目，开始批量清理...`)

      try {
        const invalidKeys = invalidEntries.map(entry => entry.key)
        await ManifestManager.batchRemoveEntries(invalidKeys)
        this.logger.log(`[ResourceService] 已批量清理 ${invalidEntries.length} 个无效缓存条目`)
      } catch (error) {
        this.logger.error('[ResourceService] 批量清理缓存条目失败:', error)
        // 如果批量清理失败，回退到逐个清理
        for (const entry of invalidEntries) {
          try {
            await ManifestManager.removeEntry(entry.key)
            this.logger.log(`[ResourceService] 已清理无效缓存条目: ${entry.url}`)
          } catch (error) {
            this.logger.error(`[ResourceService] 清理缓存条目失败: ${entry.key}`, error)
          }
        }
      }
    }

    this.logger.log(`[ResourceService] 缓存完整性验证完成 - 有效: ${validCount}, 无效已清理: ${invalidCount}`)
  }
}
