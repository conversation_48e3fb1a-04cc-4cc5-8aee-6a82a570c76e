{"root": ["./src/appinitconfig.ts", "./src/modulerunner.ts", "./src/index.ts", "./src/root.module.ts", "./src/app-modules/applicationterminatoronlastwindowclose.ts", "./src/app-modules/autoupdater.ts", "./src/app-modules/blocknotallowdorigins.ts", "./src/app-modules/chromedevtoolsextension.ts", "./src/app-modules/externalurls.ts", "./src/app-modules/hardwareaccelerationmodule.ts", "./src/app-modules/singleinstanceapp.ts", "./src/app-modules/windowmanager.ts", "./src/infra/models/uploadtaskmodel.ts", "./src/infra/types/abstractsecuritymodule.ts", "./src/infra/types/appmodule.ts", "./src/infra/types/baseipchandler.ts", "./src/infra/types/baserepository.ts", "./src/infra/types/crudablebaseipchandler.ts", "./src/infra/types/crudablebaseservice.ts", "./src/infra/types/modulecontext.ts", "./src/infra/utils/ffmpeg.ts", "./src/infra/utils/fs.ts", "./src/modules/crudable/crudable.module.ts", "./src/modules/crudable/upload-task/types.ts", "./src/modules/crudable/upload-task/upload-queue-manager.ts", "./src/modules/crudable/upload-task/upload-task.ipc-handler.ts", "./src/modules/crudable/upload-task/upload-task.module.ts", "./src/modules/crudable/upload-task/upload-task.repository.ts", "./src/modules/crudable/upload-task/upload-task.service.ts", "./src/modules/crudable/upload-task/upload.worker.ts", "./src/modules/editor/editor.ipc-handler.ts", "./src/modules/editor/editor.module.ts", "./src/modules/editor/editor.service.ts", "./src/modules/editor/keyframe-extractor.service.ts", "./src/modules/file-downloader/file-downloader.ipc-handler.ts", "./src/modules/file-downloader/file-downloader.module.ts", "./src/modules/file-downloader/file-downloader.service.ts", "./src/modules/file-uploader/file-uploader.ipc-handler.ts", "./src/modules/file-uploader/file-uploader.module.ts", "./src/modules/file-uploader/file-uploader.service.ts", "./src/modules/global/auto-updater.ipc-handler.ts", "./src/modules/global/auto-updater.service.ts", "./src/modules/global/base-info.ipc-handler.ts", "./src/modules/global/database.service.ts", "./src/modules/global/global.module.ts", "./src/modules/global/request.service.ts", "./src/modules/global/request.types.ts", "./src/modules/mixcut/mixcut.ipc-handler.ts", "./src/modules/mixcut/mixcut.module.ts", "./src/modules/mixcut/mixcut.service.ts", "./src/modules/resource/resource.ipc-handler.ts", "./src/modules/resource/resource.module.ts", "./src/modules/resource/resource.service.ts", "./src/modules/resource/resource.utils.ts", "./src/modules/window-manager/window-manager.ipc-handler.ts", "./src/modules/window-manager/window-manager.module.ts", "../../types/env.d.ts"], "version": "5.8.3"}