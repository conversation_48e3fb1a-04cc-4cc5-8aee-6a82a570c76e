import React, { useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Checkbox } from '@/components/ui/checkbox'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { RefreshCw, Play, Eye, ShoppingCart, MapPin } from 'lucide-react'
import { DetailDOS, Account, MountType, Setting } from '@/types/matrix/douyin'
import { SimpleVideo } from './VideoSelectionSection'
import { buildDetailDOS } from '../utils/buildDetailDOS'
import { usePreviewPublish, PreviewMatrixParams } from '../contexts/PreviewPublishContext'
import { VideoPreviewPopover } from '../../components/VideoPreviewModal'
import { AuthedImgByObjectId } from '@/components/authed-img'

// 组件 ref 接口
export interface PreviewPublishDialogRef {
  open: (data: PreviewMatrixParams) => void
  close: () => void
}

interface PreviewPublishDialogProps {
  onConfirm: (selectedDetailDOS: DetailDOS[]) => void
}

// 按账号分组的数据结构
interface AccountGroup {
  account: Account
  details: DetailDOS[]
}

export const PreviewPublishDialog = forwardRef<PreviewPublishDialogRef, PreviewPublishDialogProps>(
  ({ onConfirm }, ref) => {
    const [open, setOpen] = useState(false)
    const [selectedDetailIds, setSelectedDetailIds] = useState<Set<string>>(new Set())
    const { previewData, setPreviewData, detailDOS, setDetailDOS } = usePreviewPublish()

    // 生成统一的 detailId
    const generateDetailId = (detail: DetailDOS, index: number) => {
      return `${detail.accountId}-${detail.url}-${index}`
    }

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      open: (data: PreviewMatrixParams) => {
        setPreviewData(data)
        setDetailDOS(data.detailDOS)
        // 默认全选
        const allIds = data.detailDOS.map((detail, index) => generateDetailId(detail, index))
        setSelectedDetailIds(new Set(allIds))
        setOpen(true)
      },
      close: () => {
        setOpen(false)
        setPreviewData(null)
        setDetailDOS([])
        setSelectedDetailIds(new Set())
      }
    }), [setPreviewData, setDetailDOS])

    // 按账号分组数据
    const accountGroups = useMemo(() => {
      if (!previewData) return []

      const groups: AccountGroup[] = []
      const accountMap = new Map<string, Account>()

      // 构建账号映射，使用传入的完整账号信息
      previewData.accounts.forEach(account => {
        accountMap.set(account.id.toString(), account)
      })

      // 按账号分组
      detailDOS.forEach(detail => {
        const accountId = detail.accountId
        let group = groups.find(g => g.account.id.toString() === accountId)

        if (!group) {
          const account = accountMap.get(accountId)
          if (account) {
            group = { account, details: [] }
            groups.push(group)
          }
        }

        if (group) {
          group.details.push(detail)
        }
      })

      return groups
    }, [previewData, detailDOS])

    // 重新分配视频
    const handleRegenerate = () => {
      if (!previewData) return

      // 使用传入的完整账号信息
      const accounts = previewData.accounts

      const videos: SimpleVideo[] = previewData.videoList.map((video, index) => ({
        url: video.url || '',
        cover: video.cover,
        name: previewData.titles[index] || `视频${index + 1}`
      }))

      const newDetailDOS = buildDetailDOS(
        accounts,
        videos,
        previewData.titles,
        previewData.accountProducts,
        previewData.setting,
        previewData.poi.poiId,
        previewData.poi.poiName
      )

      setDetailDOS(newDetailDOS)

      // 重新设置选中状态
      const allIds = newDetailDOS.map((detail, index) => generateDetailId(detail, index))
      setSelectedDetailIds(new Set(allIds))
    }

    // 处理单个选择
    const handleDetailSelect = (detailId: string, checked: boolean) => {
      const newSelected = new Set(selectedDetailIds)
      if (checked) {
        newSelected.add(detailId)
      } else {
        newSelected.delete(detailId)
      }
      setSelectedDetailIds(newSelected)
    }

    // 处理全选/取消全选
    const handleSelectAll = (checked: boolean) => {
      if (checked) {
        const allIds = detailDOS.map((detail, index) => generateDetailId(detail, index))
        setSelectedDetailIds(new Set(allIds))
      } else {
        setSelectedDetailIds(new Set())
      }
    }

    // 确认发布
    const handleConfirm = () => {
      const selectedDetails = detailDOS.filter((detail, index) => {
        const detailId = generateDetailId(detail, index)
        return selectedDetailIds.has(detailId)
      })
      console.log({ selectedDetails })

      onConfirm(selectedDetails)
      setOpen(false)
    }

    if (!previewData) return null

    const selectedCount = selectedDetailIds.size
    const totalCount = detailDOS.length

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-6xl w-[1200px] max-h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              发布预览 - {previewData.name}
            </DialogTitle>
            <DialogDescription>
              预览即将发布的视频分配情况，您可以重新分配或选择部分内容进行发布
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center justify-between py-2">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerate}
                className="flex items-center gap-2"
              >
                <RefreshCw className="w-4 h-4" />
                重新分配
              </Button>

              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selectedCount === totalCount && totalCount > 0}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm">全选</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                已选择 {selectedCount}/{totalCount}
              </Badge>
              <Badge variant="outline">
                {previewData.setting === Setting.ONE_ACCOUNT_ONE_VIDEO ? '一账号一视频' : '一账号多视频'}
              </Badge>
            </div>
          </div>

          <Separator />

          <ScrollArea className="flex-1 pr-4">
            <div className="space-y-4">
              {accountGroups.map((group, groupIndex) => (
                <Card key={`${group.account.id}-${groupIndex}`}>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-base">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={group.account.avatar} />
                        <AvatarFallback>{group.account.nickname?.charAt(0) || 'A'}</AvatarFallback>
                      </Avatar>
                      {group.account.nickname || group.account.orgNickname || `账号${group.account.id}`}
                      <Badge variant="secondary" className="ml-2">
                        {group.details.length} 个视频
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {group.details.map(detail => {
                      // 找到该 detail 在原始 detailDOS 数组中的索引
                      const originalIndex = detailDOS.findIndex(d =>
                        d.accountId === detail.accountId &&
                        d.url === detail.url &&
                        d.title === detail.title
                      )
                      const detailId = generateDetailId(detail, originalIndex)
                      const isSelected = selectedDetailIds.has(detailId)

                      return (
                        <div
                          key={detailId}
                          className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                            isSelected ? 'bg-background border-neutral-600' : 'bg-background/50 border-neutral-800'
                          }`}
                        >
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={checked => handleDetailSelect(detailId, checked as boolean)}
                          />

                          <div className="flex items-center gap-3 flex-1">
                            <div className="relative">
                              <AuthedImgByObjectId
                                src={detail.cover}
                                alt="视频封面"
                                className="w-16 h-20 object-cover rounded"
                              />
                              <VideoPreviewPopover
                                video={{ url: detail.url, cover: detail.cover, name: detail.title }}
                                trigger={
                                  <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded cursor-pointer opacity-0 hover:opacity-100 transition-opacity">
                                    <Play className="w-6 h-6 text-white" />
                                  </div>
                                }
                              />
                            </div>

                            <div className="flex-1 space-y-1">
                              <h4 className="font-medium text-sm line-clamp-2">{detail.title}</h4>

                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                {previewData.mountType === MountType.LOCATION && detail.poiName && (
                                  <div className="flex items-center gap-1">
                                    <MapPin className="w-3 h-3" />
                                    <span>{detail.poiName}</span>
                                  </div>
                                )}

                                {previewData.mountType === MountType.CART && detail.products.length > 0 && (
                                  <div className="flex items-center gap-1">
                                    <ShoppingCart className="w-3 h-3" />
                                    <span>{detail.products.length} 个商品</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>

          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              取消
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={selectedCount === 0}
            >
              确认发布 ({selectedCount})
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
)
