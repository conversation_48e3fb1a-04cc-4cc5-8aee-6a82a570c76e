import { Track, TrackType } from '@/modules/video-editor/types'
import { Overlay, OverlayType, StoryboardOverlay } from '@clipnest/remotion-shared/types'
import {
  DraggableState,
  OverlayDragInfo,
  OverlaysAdjustment,
  useEditorContext,
  useTimelineContext,
  useTimelineDnd
} from '@/modules/video-editor/contexts'
import {
  byStartFrame,
  calculateLeftSpaceOfStoryboard,
  findOverlaysAboveStorybook,
  findOverlaysBetweenFrames,
  findTrackByOverlay,
  getOverlayTimeRange,
  isValidProgressiveOverlay
} from '@/modules/video-editor/utils/overlay-helper'
import { findStoryboardByFromFrame } from '@/modules/video-editor/utils/track-helper'
import { useCallback } from 'react'
import { snapToGrid } from '@/modules/video-editor/contexts/timeline-dnd/utils'
import { PIXELS_PER_FRAME } from '@/modules/video-editor/constants'

function calcMaxDuration(overlay: Overlay) {
  if (isValidProgressiveOverlay(overlay)) {
    return (overlay.originalDurationFrames - (overlay.trimStartFrames ?? 0)) / (overlay.speed ?? 1)
  }
  return null
}

function calcAdjustForResize(
  tracks: Track[],
  currentOverlay: Overlay,
  restOverlays: Overlay[],
  intendedNewDuration: number,
  targetStoryboard: Overlay | null,
  shouldFillGap?: boolean
): Pick<Required<DraggableState>, 'adjustedDuration' | 'overlaysAdjust'> {
  // let trimEndRatio = 0
  let adjustedDuration = intendedNewDuration

  // 当在分镜中调整时长时, 需要考虑分镜内的剩余空间, 并限制时长
  if (targetStoryboard && currentOverlay.type !== OverlayType.STORYBOARD) {
    // 计算剩余空间时，需要把当前 Overlay 也包含在内
    const leftSpace = calculateLeftSpaceOfStoryboard(
      targetStoryboard,
      [currentOverlay, ...restOverlays]
    )

    adjustedDuration = Math.min(
      currentOverlay.durationInFrames + leftSpace,
      intendedNewDuration
    )
  }

  const maxDuration = calcMaxDuration(currentOverlay)
  if (maxDuration !== null) {
    adjustedDuration = Math.min(adjustedDuration, maxDuration)
  }

  const overlaysAdjust: OverlaysAdjustment = new OverlaysAdjustment()

  const [, currentOverlayEndFrame] = getOverlayTimeRange(currentOverlay)

  const intendedNewEnd = adjustedDuration + currentOverlay.from

  const affectedOverlays = findOverlaysBetweenFrames(
    restOverlays,
    currentOverlayEndFrame - 1,
    Infinity,
    'start'
  )

  if (!affectedOverlays.length) {
    return { overlaysAdjust, adjustedDuration }
  }

  // 移动当前 Overlay 后方的所有 Overlay, 以补齐空位或避免重叠
  // 如果当前 Overlay 是分镜 Overlay, 则该分镜下的所有 Overlay 也需要同步移动
  const allOverlays = currentOverlay.type === OverlayType.STORYBOARD
    ? affectedOverlays
      .filter((o): o is StoryboardOverlay & { index: number } => o.type === OverlayType.STORYBOARD)
      .map(storyboard => {
        return [storyboard, ...findOverlaysAboveStorybook(tracks, storyboard)]
      })
      .flat() as Overlay[]
    : affectedOverlays

  const durationShift = intendedNewEnd - affectedOverlays[0].from

  if (durationShift > 0 || shouldFillGap) {
    allOverlays.forEach(storyboard => {
      overlaysAdjust.set(storyboard.id, { fromFrameShift: durationShift })
    })
  }

  return { overlaysAdjust, adjustedDuration }
}

function calculateDraggableState(
  tracks: Track[],
  currentOverlay: Overlay,
  intendedNewDuration: number,
): DraggableState {
  if (!currentOverlay) {
    return {
      draggable: false,
    }
  }

  const track = findTrackByOverlay(tracks, currentOverlay.id) || null
  if (!track) {
    return { draggable: false }
  }

  const storyboard = track?.isGlobalTrack
    ? null
    : findStoryboardByFromFrame(tracks, currentOverlay.from)

  if (!track) {
    return {
      draggable: false
    }
  }

  const restOverlays = track
    .overlays
    .filter(o => (
      currentOverlay.storyboardIndex === undefined
      || o.storyboardIndex === currentOverlay.storyboardIndex
    ))
    .filter(o => o.id !== currentOverlay.id)
    .sort(byStartFrame())

  return {
    draggable: true,
    adjustedStartFrame: currentOverlay.from,
    ...calcAdjustForResize(
      tracks,
      currentOverlay,
      restOverlays,
      intendedNewDuration,
      storyboard,
      storyboard !== null && (track.type === TrackType.VIDEO || track.type === TrackType.STORYBOARD)
    )
  }
}

/**
 * 处理 TimelineItem 调整时长
 */
export const useTimelineItemResizing = () => {
  const { zoomScale } = useTimelineContext()
  const { tracks } = useEditorContext()
  const { dragInfoRef, updateDraggableState } = useTimelineDnd()

  const calculateTargetDuration = useCallback((
    dragInfo: OverlayDragInfo,
    deltaX: number,
  ) => {
    const deltaFrame = snapToGrid(deltaX / zoomScale / PIXELS_PER_FRAME)

    return Math.max(1, dragInfo.initialDurationInFrames + deltaFrame)
  }, [zoomScale])

  const handleResizeMove = useCallback(
    (deltaX: number) => {
      if (!dragInfoRef.current) return

      const targetDuration = calculateTargetDuration(
        dragInfoRef.current,
        deltaX,
      )

      dragInfoRef.current.currentDuration = targetDuration

      updateDraggableState(
        calculateDraggableState(
          tracks,
          dragInfoRef.current.overlay,
          targetDuration,
        )
      )
    },
    [tracks, calculateTargetDuration],
  )

  return {
    handleResizeMove,
  }
}
