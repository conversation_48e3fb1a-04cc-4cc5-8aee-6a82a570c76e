import { useCallback } from 'react'
import { OverlayType, SoundOverlay } from '@clipnest/remotion-shared/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useOverlayHelper } from './helpers/useOverlayHelper'
import { ResourceCacheType } from '@app/shared/types/resource-cache.types'
import { FPS } from '../constants'

export function useAddAudioToTimeLine() {
  const { addOverlayToGlobalTrack } = useOverlayHelper()

  const handleAddAudioToTimeline = useCallback(
    async (durationMsec: number, itemUrl: string, title: string) => {
      try {
        const localPath = cacheManager.resource.getResourcePathSync(ResourceCacheType.SOUND, itemUrl)
        const musicDurationInFrames = Math.round((durationMsec / 1000) * 30)
        const src = localPath ? localPath : itemUrl
        const overlay: SoundOverlay = {
          id: Date.now(),
          type: OverlayType.SOUND,
          content: title,
          src: src,
          localSrc: src,
          durationInFrames: musicDurationInFrames,
          originalDurationFrames: durationMsec / 1000 * FPS,
          from: 0,
          height: 100,
          width: 200,
          left: 0,
          top: 0,
          rotation: 0,
          styles: {
            volume: 1,
          },
        }

        addOverlayToGlobalTrack(overlay)
      } catch (error) {
        console.error('添加音乐到时间轴失败:', error)
      }
    },
    [addOverlayToGlobalTrack],
  )

  return { handleAddAudioToTimeline }
}
