import { useCallback } from 'react'
import { Overlay, OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { DEFAULT_OVERLAY, TEXT_DEFAULT_CLOUD_FONT_SRC } from '@/modules/video-editor/constants'
import {
  createTextOverlay,
  findLastOverlay,
  getOverlayTimeRange,
  getOverlayTrackIndex,
  getStoryboardAt
} from '@/modules/video-editor/utils/overlay-helper'
import { cloneDeep } from 'lodash'
import {
  calculateTrackAfterOverlayPushed,
  calculateTrackAfterOverlayRemoved,
  findStoryboardByFromFrame,
  generateNewOverlayId,
  isOverlayAcceptableByTrack,
  MAP_OVERLAY_TO_TRACK
} from '@/modules/video-editor/utils/track-helper'
import { toast } from 'react-toastify'
import { TrackType } from '@/modules/video-editor/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import opentype from 'opentype.js'

type AddOverlayPayload = {}
  & Pick<Partial<Overlay>, keyof typeof DEFAULT_OVERLAY>
  & Omit<Overlay, keyof typeof DEFAULT_OVERLAY | 'id'>

export type OverlayHelper = {
  /**
   * 向全局轨道添加 Overlay
   */
  addOverlayToGlobalTrack(overlay: AddOverlayPayload): void

  /**
   * 向指定轨道追加 overlay. 当 `startFrame` 不为空时, 则追加到其所指位置; 否则追加到轨道末尾
   */
  appendOverlayToTrack(
    trackIndex: number,
    overlay: Omit<AddOverlayPayload, 'from'>,
    startFrame?: number
  ): void

  /**
   * 处理视频速度和时长的变化
   */
  changeOverlayPlaySpeed(id: number, speed: number, newDuration: number): void

  /**
   * 移动 overlay 到指定的分镜
   */
  moveOverlayToStoryboardIndexAt(overlay: Overlay, targetStoryboardIndex: number): void

  /**
   * 移动指定位置上的 Track 到新的位置
   */
  moveTrack(fromIndex: number, toIndex: number): void

  /**
   * 为口播轨道生成默认的 TextOverlay
   */
  generateDefaultCaptionOverlay(opts?: Partial<TextOverlay>): Promise<TextOverlay>

  /**
   * 为口播轨道生成默认的 TextOverlay
   */
  generateDefaultCaptionOverlaySync(font: opentype.Font, opts?: Partial<TextOverlay>): TextOverlay

  /**
   * 在指定轨道的指定位置添加 overlay
   */
  addOverlayToTrack(trackIndex: number, payload: Omit<AddOverlayPayload, 'from'>): void
}

export const useOverlayHelper = (): OverlayHelper => {
  const {
    tracks,
    updateTracks,
    setSelectedOverlay,
    videoPlayer: { seekTo },
    getPlayerDimensions
  } = useEditorContext()

  const changeOverlayPlaySpeed = (overlayId: number, speed: number, newDuration: number) => {
    return updateTracks(prevTracks => {
      // 找到要修改的 overlay
      let overlayToUpdate: Overlay | null = null
      let trackIndex = -1

      for (let i = 0; i < prevTracks.length; i++) {
        const foundOverlay = prevTracks[i].overlays.find(o => o.id === overlayId)
        if (foundOverlay) {
          overlayToUpdate = foundOverlay
          trackIndex = i
          break
        }
      }

      if (!overlayToUpdate || trackIndex === -1) return prevTracks

      // 创建更新后的 overlay
      const updatedOverlay = {
        ...overlayToUpdate,
        speed,
        durationInFrames: newDuration
      }

      // TODO: 检查重叠并调整其他 overlays
      return [
        ...prevTracks.slice(0, trackIndex),
        {
          ...prevTracks[trackIndex],
          overlays: prevTracks[trackIndex].overlays.map(o =>
            o.id === overlayId ? updatedOverlay : o
          )
        },
        ...prevTracks.slice(trackIndex + 1)
      ]
    })
  }

  const moveOverlayToStoryboardIndexAt = (targetOverlay: Overlay, targetStoryboardIndex: number) => {
    if (targetOverlay.type !== OverlayType.VIDEO) {
      console.warn('当前暂时只支持移动视频 Overlay')
      return
    }

    return updateTracks(prevTracks => {
      const targetStoryboard = getStoryboardAt(prevTracks, targetStoryboardIndex)
      if (!targetStoryboard) {
        console.warn('目标分镜不存在')
        return prevTracks
      }

      const clonedTracks = cloneDeep(prevTracks)

      // 从原始轨道中移除目标 Overlay, 并前移后续 Overlay
      const sourceTrackIndex = getOverlayTrackIndex(clonedTracks, targetOverlay.id)
      clonedTracks[sourceTrackIndex] = calculateTrackAfterOverlayRemoved(
        clonedTracks,
        sourceTrackIndex,
        targetOverlay
      )

      // 寻找允许插入的轨道
      const targetTrack = clonedTracks
        .filter(t => t.type === TrackType.VIDEO)
        .filter(t => t.overlays.every(o => o.storyboardIndex !== targetStoryboardIndex))
        .at(0)

      if (targetTrack) {
        targetTrack.overlays.push({
          ...targetOverlay,
          storyboardIndex: targetStoryboardIndex,
          from: targetStoryboard.from
        })

        return clonedTracks
      }

      // 找不到可用轨道, 则创建新的轨道并插入
      const lastVideoTrackIndex = [...clonedTracks]
        .reverse()
        .findIndex(t => t.type === TrackType.VIDEO && !t.isGlobalTrack)

      const newTrackIndex = lastVideoTrackIndex !== -1
        ? clonedTracks.length - lastVideoTrackIndex
        : 1

      clonedTracks.splice(newTrackIndex, 0, {
        type: TrackType.VIDEO,
        overlays: [{
          ...targetOverlay,
          from: targetStoryboard.from,
          storyboardIndex: targetStoryboardIndex
        }]
      })

      return clonedTracks
    })
  }

  const appendOverlayToTrack = (trackIndex: number, payload: Omit<AddOverlayPayload, 'from'>, startFrame?: number) => {
    updateTracks(prevTracks => (
      prevTracks.map((track, index) => {
        if (index !== trackIndex) return track

        const [newTrack, newOverlay] = calculateTrackAfterOverlayPushed(prevTracks, trackIndex, payload, startFrame)

        if (newOverlay) {
          setSelectedOverlay(newOverlay)
          setTimeout(() => seekTo(newOverlay.from), 250)
        }

        return newTrack
      })
    ))
  }

  const addOverlayToTrack = (trackIndex: number, payload: AddOverlayPayload) => {
    updateTracks(prevTracks => {
      const storyboard = findStoryboardByFromFrame(prevTracks, payload.from)

      return (
        prevTracks.map((track, index) => {
          if (index !== trackIndex) return track

          if (!isOverlayAcceptableByTrack(payload, track)) {
            toast('目标轨道不支持该类型的素材', { type: 'error' })
            return track
          }

          const newOverlay = {
            ...DEFAULT_OVERLAY,
            ...payload,
            id: generateNewOverlayId(prevTracks),
            ...(storyboard && { storyboardIndex: storyboard?.index })
          } as Overlay

          setSelectedOverlay(newOverlay)
          seekTo(newOverlay.from)

          return {
            ...track,
            overlays: [...track.overlays, newOverlay]
          }
        })
      )
    })
  }

  const addOverlayToGlobalTrack = (overlay: AddOverlayPayload) => {
    updateTracks(prevTracks => {
      const targetTrackType = MAP_OVERLAY_TO_TRACK[overlay.type]
      const targetTrackIndex = prevTracks.findIndex(t => t.isGlobalTrack && t.type === targetTrackType)
      const id = generateNewOverlayId(prevTracks)

      // 找不到合适的全局轨道, 则自动创建一条并添加
      if (targetTrackIndex === -1) {
        return [
          ...prevTracks,
          {
            type: targetTrackType,
            isGlobalTrack: true,
            overlays: [{
              ...DEFAULT_OVERLAY,
              ...overlay,
              id
            } as Overlay]
          }
        ]
      }

      const targetTrack = cloneDeep(prevTracks[targetTrackIndex])
      const [, lastOverlayEnd] = getOverlayTimeRange(findLastOverlay(targetTrack.overlays))

      targetTrack.overlays.push({
        ...DEFAULT_OVERLAY,
        ...overlay,
        id,
        from: lastOverlayEnd,
      } as Overlay)

      return [
        ...prevTracks.slice(0, targetTrackIndex),
        targetTrack,
        ...prevTracks.slice(targetTrackIndex + 1)
      ]
    })
  }

  const moveTrack = (fromIndex: number, toIndex: number) => {
    updateTracks(prevTracks => {
      if (fromIndex < 0 || fromIndex >= prevTracks.length || toIndex < 0 || toIndex >= prevTracks.length) {
        return prevTracks
      }

      const newTracks = cloneDeep(prevTracks)
      // 保存要移动的 track
      const trackToMove = newTracks[fromIndex]

      // 从原位置删除
      newTracks.splice(fromIndex, 1)

      // 插入到目标位置
      newTracks.splice(toIndex, 0, trackToMove)

      return newTracks
    })
  }

  const generateDefaultCaptionOverlaySync = useCallback(
    (font: opentype.Font, opts?: Partial<TextOverlay>) => {
      const {
        content = '默认文字',
        id = generateNewOverlayId(tracks),
        styles,
        ...restDefaultValues
      } = opts || {}

      const { playerWidth, playerHeight } = getPlayerDimensions()

      return createTextOverlay(font, {
        ...restDefaultValues,
        id,
        content,
        width: playerWidth * 0.8,
        left: playerWidth * 0.1,
        top: playerHeight * 0.8,
      }) as TextOverlay
    },
    [tracks]
  )

  const generateDefaultCaptionOverlay = useCallback(
    async (opts?: Partial<TextOverlay>) => {
      return generateDefaultCaptionOverlaySync(
        await cacheManager.font.cacheFont(TEXT_DEFAULT_CLOUD_FONT_SRC),
        opts
      )
    },
    [generateDefaultCaptionOverlaySync]
  )

  return {
    addOverlayToGlobalTrack,
    appendOverlayToTrack,
    changeOverlayPlaySpeed,
    moveOverlayToStoryboardIndexAt,
    moveTrack,
    addOverlayToTrack,
    generateDefaultCaptionOverlay,
    generateDefaultCaptionOverlaySync,
  }
}
