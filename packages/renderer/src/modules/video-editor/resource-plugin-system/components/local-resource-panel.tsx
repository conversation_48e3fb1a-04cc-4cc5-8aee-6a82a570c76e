import React, { <PERSON>actN<PERSON>, useEffect, useMemo, useState } from 'react'
import { PlusIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TreeNode } from '@/components/TreeList'
import { cn } from '@/components/lib/utils'
import MoveDialog from '@/components/material/MoveDialog'
import { useItemActions } from '@/hooks/useItemActions'
import { CloudResourceTypes, MaterialResource, PasterResource, ResourceSource, SoundResource } from '@/types/resources'
import { ResourceModule } from '@/libs/request/api/resource'
import { useSelection } from '@/hooks/useSelection'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { useFolderData } from '@/hooks/useFolderData'
import { UploadTask } from '@app/shared/types/upload-task.types.js'
import { GenericResourceModule } from '@/libs/request/api/generic-resource'
import {
  EditorDraggableTypes,
  EditorDroppableTypes,
  useTypedDraggable,
  useTypedDroppable
} from '../../components/editor-dnd-wrapper'
import { LocalDirItem, LocalDirItemProps } from './local-dir'
import { toast } from 'react-toastify'
import { FileOrFolderUploader } from '@/components/material/FileOrFolderUploader'
import { FileCategory } from '@app/shared/file'
import { UploadingTasks } from '@/modules/task-center/components/UploadingTasks'

export type Folder = {
  id: string
  name: string
}

export interface LocalResourcePanelProps<T> {
  /**
   * 目录数据
   */
  dirList: TreeNode[]
  /**
   * 当前选中的目录
   */
  currentFolderId: string
  /**
   * 切换目录回调
   */
  onFolderChange: (folderId: string) => void // 新增: 切换目录回调
  /**
   * 本地资源类型
   */
  resourceType: ResourceSource
  /**
   * 本地资源文件夹类型
   */
  resourceFolderType: ResourceSource
  /**
   * 文件上传类型
   */
  fileUploadTypes: FileCategory[]

  /**
   * 上传完成回调
   */
  onUploadComplete?: () => void
  /**
   * 关键词
   */
  searchKey?: string

  /**
   * 添加进轨道回调
   */
  onAddToTimeline?: (resource: T) => void
  /**
   * 资源列表
   */
  resources?: any
  /**
   * 是否显示上传按钮
   */
  showUpload?: boolean
  /**
   * 是否显示新建文件夹按钮
   */
  showCreateFolder?: boolean
  /**
   * 列表为空时的提示文本
   */
  emptyText?: string
  /**
   * 列表容器的类名
   */
  containerClassName?: string
  /**
   * 资源网格的列数
   */
  gridCols?: number
  /**
   * 是否加载中
   */
  isLoading?: boolean
  /**
   * 资源项渲染函数
   */
  renderResourceItem?: (resource: T, index: number) => ReactNode
}

/**
 * 通用本地资源面板组件
 * 提供文件夹选择、上传文件、新建文件夹功能，以及资源列表展示
 */
export function LocalResourcePanel<T extends PasterResource.PasterLocal | SoundResource.SoundLocal>({
  dirList,
  currentFolderId,
  onFolderChange,
  resourceType,
  resourceFolderType,
  fileUploadTypes,
  onUploadComplete,
  // searchKey = '',
  onAddToTimeline,
  resources = {},
  showUpload = true,
  showCreateFolder = true,
  renderResourceItem,
  emptyText = '暂无资源',
  containerClassName = '',
  gridCols = 4,
  isLoading = false,
}: LocalResourcePanelProps<T>) {
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('') //被移动的文件夹id
  const [moveType, setMoveType] = useState(resourceType)
  const { createItem, deleteLocalItem, moveItem, invalidate } = useItemActions()
  const flatResourcesLength = Array.isArray(resources?.pages)
    ? resources.pages.reduce((count, page) => count + (page?.list?.length || 0), 0)
    : 0
  const [processedResources, setProcessedResources] = useState<T[]>([])

  // 获取根目录的目录树
  const rootDirs = useMemo(() => {
    return dirList.filter(item => item.raw?.parentId === null)
  }, [dirList])

  // 获取目录链
  const { folderPath, childFolders: originalChildFolders } = useFolderData({
    currentFolderId,
    treeData: rootDirs,
  })

  // 获取当前目录下的子目录根据筛选词筛选
  // const childFolders = useMemo(() => {
  //   if (!searchKey) return originalChildFolders
  //   const keyword = searchKey.toLowerCase()
  //   return originalChildFolders.filter(child => child.label?.toLowerCase().includes(keyword))
  // }, [searchKey, originalChildFolders])

  // 本地文件夹操作
  const LocalFolderActions = useFolderActions(
    true,
    resourceFolderType,
    originalChildFolders,
    setMoveType,
    setMoveId,
    setMoveDialogOpen,
  )
  const LocalMediaActions = useMediaActions(true, resourceType, setMoveType, setMoveId, setMoveDialogOpen)

  // 获取选择相关数据
  const {
    selectedMediaItems,
    setSelectedMediaItems,
    toggleSelect,
    toggleSelectAll,
    allSelected,
    selectedCount,
    materialCount,
  } = useSelection({
    mediaList: resources,
    getMediaId: (media: { fileId: string }) => media.fileId,
  })

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    // 移动媒体文件
    if (selectedMediaItems.size > 0) {
      try {
        await moveItem(resourceType, Array.from(selectedMediaItems), selectedNode.id)
        setSelectedMediaItems(new Set())
      } catch (error) {
        if (error.message === '存储目录已经存在') toast('目标目录里存在同名文件', { type: 'warning' })
      }
    }
  }

  //获取可用url
  const extractFileId = (url: string) => {
    try {
      const parsedUrl = new URL(url)
      const pathSegments = parsedUrl.pathname.split('/')
      return pathSegments[pathSegments.length - 1] // 获取 URL 路径中的最后一部分作为 ID
    } catch (error) {
      console.error('URL 解析失败:', error)
      return ''
    }
  }

  const processResources = async (resourceList: any[]) => {
    const replaceUrl = async (url: string) => {
      if (!url) return ''
      const objectId = extractFileId(url)
      const res = await ResourceModule.cover(objectId)
      const parser = new DOMParser()
      const doc = parser.parseFromString(res, 'text/html')
      const anchor = doc.querySelector('a')
      return anchor?.href || url // 如果解析失败，保持原 URL
    }

    return await Promise.all(
      resourceList.map(async resource => {
        try {
          if (resourceType === ResourceSource.LOCAL_STICK) {
            const fileUrl = await replaceUrl(resource.content?.fileUrl)
            const thumbUrl = await replaceUrl(resource.content?.thumbUrl)
            const coverUrl = await replaceUrl(resource.cover?.url)

            return {
              ...resource,
              content: {
                ...resource.content,
                fileUrl,
                thumbUrl,
              },
              cover: {
                ...resource.cover,
                url: coverUrl,
              },
            }
          } else if (resourceType === ResourceSource.LOCAL_SOUND || resourceType === ResourceSource.LOCAL_MUSIC) {
            const itemUrl = await replaceUrl(resource.content?.itemUrl)
            return {
              ...resource,
              content: {
                ...resource.content,
                itemUrl,
              },
            }
          }
        } catch (error) {
          console.error('获取地址失败:', error)
          return resource
        }
      }),
    )
  }

  const onLocalItemAdd = async (item: T) => {
    console.log('点击本地资源项-添加进轨道', item)

    onAddToTimeline?.(item)
  }

  const onLocalFolderAdd = async (item: TreeNode) => {
    console.log('点击本地文件夹-添加进轨道', item)
    try {
      let fetchFn
      switch (resourceType) {
        case ResourceSource.LOCAL_STICK:
          fetchFn = ResourceModule.paster.localList
          break
        case ResourceSource.LOCAL_MUSIC:
          fetchFn = ResourceModule.music.localList
          break
        case ResourceSource.LOCAL_SOUND:
          fetchFn = ResourceModule.voice.localList
          break
        default:
          console.warn('不支持的资源类型:', resourceType)
          return
      }
      const res = await fetchFn({
        pageSize: 100, // 取足够多的文件
        folderUuid: item.id,
        pageNum: 1,
      })

      const folderResources = res?.list || []
      const updatedResources = await processResources(folderResources)

      if (updatedResources.length > 0) {
        updatedResources.forEach(item => onAddToTimeline?.(item))
      } else {
        console.log('该文件夹下没有文件资源')
      }
    } catch (err) {
      console.error('获取文件夹资源失败:', err)
    }
  }

  //上传完成之后创建本地资源
  const handleUploadComplete = async (tasks: UploadTask.CompleteEvent) => {
    const successTasks = tasks.tasks.filter(task => task.status === UploadTask.Status.COMPLETED)

    for (const file of successTasks) {
      try {
        await GenericResourceModule.endpoints.teamCreate(GenericResourceModule.ResourceTypeByResourceSource[resourceType], {
          folderUuid: file.folder_id,
          title: file.name,
          fileMd5: file.hash,
          contentType: GenericResourceModule.ResourceTypeByResourceSource[resourceType],
          objectId: file.object_id,
        })
      } catch (err) {
        console.error('上传失败：', err)
      }
    }

    onUploadComplete?.()
  }

  //彻底删除
  const handleDelete = async () => {
    if (selectedMediaItems.size > 0) {
      await deleteLocalItem(resourceType, Array.from(selectedMediaItems), `${selectedCount}个资源文件`)
    }

    await invalidate([resourceType])
    setSelectedMediaItems(new Set())
  }

  useEffect(() => {
    setSelectedMediaItems(new Set())
  }, [currentFolderId])

  //获取fileUrl 和 thumbUrl
  useEffect(() => {
    const fetchCovers = async () => {
      if (!resources?.pages) return

      const allResources = resources.pages.flatMap(page => page.list)
      const updatedResources = await processResources(allResources)

      setProcessedResources(updatedResources)
    }

    fetchCovers()
  }, [resources])

  // 校验 currentFolderId 是否存在于根目录树，如果不存在则重置
  useEffect(() => {
    const findNodeInTree = (nodes: TreeNode[], id: string): boolean => {
      for (const node of nodes) {
        if (String(node.id) === String(id)) return true
        if (node.children && findNodeInTree(node.children, id)) return true
      }
      return false
    }

    if (!findNodeInTree(rootDirs, currentFolderId) && rootDirs.length > 0) {
      // 如果当前 folderId 不在筛选出的树中，则重置为第一个 root 目录 id
      onFolderChange(rootDirs[0].id)
    }
  }, [rootDirs, currentFolderId, onFolderChange])

  return (
    <div className={`flex flex-col gap-4 mt-2 ${containerClassName}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 w-full justify-between">
          {showUpload && (
            <FileOrFolderUploader
              folderUuid={currentFolderId}
              variant="popover"
              library={GenericResourceModule.ResourceTypeByResourceSource[resourceType]}
              fileCategory={fileUploadTypes}
              folderOptions={{
                directoryType: 'team',
                apiPrefix: GenericResourceModule.ResourceTypeByResourceSource[resourceType]
              }}
              onComplete={(payload: UploadTask.CompleteEvent) => handleUploadComplete(payload)}
            />

          )}
          {showCreateFolder && (
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
              onClick={() =>
                createItem(
                  resourceFolderType,
                  currentFolderId,
                  {
                    label: '文件夹名称',
                    headerTitle: '文件夹',
                  },
                  originalChildFolders,
                )}
            >
              <PlusIcon className="w-3.5 h-3.5" />
              <span>新建文件夹</span>
            </Button>
          )}
        </div>
      </div>
      {selectedCount ? (
        <div className="flex items-center justify-between text-sm ">
          <div className="flex justify-end items-center space-x-4">
            <span>总数：{materialCount}</span>
            <label className="flex items-center cursor-pointer">
              <input
                type="radio"
                checked={allSelected}
                onClick={toggleSelectAll}
                readOnly
                className="accent-primary-highlight1"
              />
            </label>
            <span>已选 {selectedCount}</span>
          </div>
          <div>
            <Button
              variant="link"
              size="sm"
              className="text-primary-highlight1"
              onClick={() => {
                let type = ResourceSource.LOCAL_MUSIC
                if (resourceType === ResourceSource.LOCAL_MUSIC) {
                  type = ResourceSource.LOCAL_MUSIC_MULTI_SELECT
                } else if (resourceType === ResourceSource.LOCAL_SOUND) {
                  type = ResourceSource.LOCAL_SOUND_MULTI_SELECT
                } else if (resourceType === ResourceSource.LOCAL_STICK) {
                  type = ResourceSource.LOCAL_STICK_MULTI_SELECT
                }
                setMoveType(type)
                setMoveDialogOpen(true)
              }}
            >
              移动到
            </Button>
            <span className="text-primary-highlight1"> | </span>
            <Button variant="link" size="sm" className="text-primary-highlight1 " onClick={handleDelete}>
              删除
            </Button>
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center space-x-1 text-white">
            {folderPath.map((folder, index) => (
              <React.Fragment key={folder.id}>
                <button
                  className={cn('hover:underline', {
                    'text-primary-highlight1': folder.id === currentFolderId,
                  })}
                  onClick={() => onFolderChange(folder.id)}
                >
                  {folder.label}
                </button>
                {index < folderPath.length - 1 && <span>{'>'}</span>}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}
      {/* 资源列表区域 */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="text-gray-400">加载中...</div>
        </div>
      ) : (
        <>
          {originalChildFolders.length > 0 || flatResourcesLength > 0 ? (
            <div className={`grid grid-cols-${gridCols} gap-3 mb-4`}>
              {/* 渲染子文件夹 */}
              {originalChildFolders.map(folder => (
                <DroppableLocalDirItem
                  materialType={resourceFolderType}
                  key={folder.id}
                  folder={folder}
                  isSelected={false}
                  currentFolderId={currentFolderId}
                  onFolderChange={onFolderChange}
                  LocalActions={LocalFolderActions}
                  onItemAdd={() => onLocalFolderAdd(folder)}
                />
              ))}
              {/* 渲染本地资源 */}
              {processedResources.map((resource, index) => (
                <LocalDirItem
                  key={resource.fileId || index}
                  resource={resource}
                  isResource={true}
                  index={index}
                  currentFolderId={currentFolderId}
                  onFolderChange={onFolderChange}
                  LocalActions={LocalMediaActions}
                  renderResourceItem={renderResourceItem}
                  onItemAdd={() => onLocalItemAdd(resource)}
                  isSelected={selectedMediaItems.has(resource.fileId)}
                  onToggleSelect={toggleSelect}
                />
              ))}
              <UploadingTasks library={GenericResourceModule.ResourceTypeByResourceSource[resourceType]} />

            </div>
          ) : (
            <div className="flex justify-center items-center h-40">
              <div className="text-gray-400">{emptyText}</div>
            </div>
          )}
        </>
      )}
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        dirList={dirList}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default LocalResourcePanel

export interface DroppableLocalDirItemProps
  extends Omit<
    LocalDirItemProps<PasterResource.PasterLocal | SoundResource.SoundLocal>,
    'resource' | 'renderResourceItem'
  > {
  folder: TreeNode
  materialType: ResourceSource
}

export const DroppableLocalDirItem = ({ folder, materialType, ...rest }: DroppableLocalDirItemProps) => {
  const folderAsMediaItems = useMemo<MaterialResource.Media>(() => {
    return {
      fileId: folder.id,
      fileName: folder.label,
      folderUuid: folder.raw!.parentId!,
      childrenFolder: folder.children?.length || 0,
      mediaNum: folder.raw!.imageCount + folder.raw!.videoCount + folder.raw!.audioCount,
      resType: 0,
      createTime: folder.raw!.createdAt || undefined,
      url: '',
    }
  }, [folder])

  const { setNodeRef: setDroppableRef } = useTypedDroppable(EditorDroppableTypes.Folder, folder.id, {
    resource: {
      ...folderAsMediaItems,
      materialType: materialType,
    },
  })

  const {
    setNodeRef: setDraggableRef,
    listeners,
    attributes,
  } = useTypedDraggable(EditorDraggableTypes.Resource, folder.id, {
    resourceType: CloudResourceTypes.MATERIAL,
    data: {
      ...folderAsMediaItems,
      materialType: materialType,
    },
  })

  // 合并 ref
  const combinedRef = (node: HTMLElement | null) => {
    setDroppableRef(node)
    setDraggableRef(node)
  }

  return (
    <div ref={combinedRef} {...listeners} {...attributes}>
      <LocalDirItem folder={folder} {...rest} />
    </div>
  )
}

