import { Overlay, OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { FPS } from './basic-configs'
import { Track } from '@/modules/video-editor/types'

// const OVERLAY_PLACEHOLDER: Omit<Overlay, 'type'> = {
//   id: 0,
//   left: 0,
//   top: 0,
//   width: 0,
//   height: 0,
//   from: 0,
//   durationInFrames: 0,
//   rotation: 0
// }

export const TEXT_DEFAULT_CLOUD_FONT_SRC = 'https://clipnest-library.oss-cn-shanghai.aliyuncs.com/resources/fonts/file_v2/6e4bee66c618418c9c8a23578e5bd468.ttf'
export const TEXT_DEFAULT_CLOUD_FONT_NAME = '阿里妈妈数黑体'

export const DEFAULT_TEXT_OVERLAY_STYLES: TextOverlay['styles'] = {
  fontSize: 150,
  fontWeight: 'normal',
  color: '#fff',
  backgroundColor: 'transparent',
  fontFamily: TEXT_DEFAULT_CLOUD_FONT_NAME,
  fontStyle: 'normal',
  underlineEnabled: false,
  lineSpacing: 0.2,
  textAlign: 'center',
  letterSpacing: 0,

  // 描边属性
  strokeEnabled: false,
  strokeWidth: 0,
  strokeColor: '#000000',

  // 阴影属性
  shadowEnabled: false,
  shadowDistance: 0,
  shadowAngle: 0,
  shadowBlur: 0,
  shadowColor: '#000000',
  shadowOpacity: 1,

  // 透明度
  textOpacity: 1,
  backgroundOpacity: '1',

  // 布局属性
  padding: 0.05, // 默认两侧各留5%空隙

  // 基础样式
  opacity: 1,
  zIndex: 1,
  transform: undefined
}

export const DEFAULT_OVERLAY: Pick<
  Overlay,
  'width' | 'height' | 'left' | 'top' | 'rotation' | 'durationInFrames'
> = {
  width: 0,
  height: 0,
  left: 0,
  top: 0,
  rotation: 0,
  durationInFrames: FPS * 3
} as const

export const DEFAULT_TEXT_OVERLAY: TextOverlay = {
  ...DEFAULT_OVERLAY,
  id: 0,
  from: 0,
  content: '默认文字',
  src: TEXT_DEFAULT_CLOUD_FONT_SRC,
  type: OverlayType.TEXT,
  styles: DEFAULT_TEXT_OVERLAY_STYLES
}

export const DEFAULT_TRACKS: Track[] = [
]
