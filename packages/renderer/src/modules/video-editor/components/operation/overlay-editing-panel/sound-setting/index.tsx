import React, { useEffect, useRef, useState } from 'react'
import { SoundOverlay } from '@clipnest/remotion-shared/types'
import { Pause, Play } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { MediaControls } from '@/modules/video-editor/components/common/media-controls'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { AudioFadeControls } from './audio-fade-control'
import { SoundControls } from '../../../shared/sound-controls'

export const SoundSetting: React.FC = () => {
  const { localOverlay, updateEditingOverlay } = useOverlayEditing<SoundOverlay>()

  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    audioRef.current = new Audio(localOverlay.src)
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
      }
    }
  }, [localOverlay.src])

  const togglePlay = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    }
    else {
      audioRef.current
        .play()
        .catch(error => console.error('Error playing audio:', error))
    }
    setIsPlaying(!isPlaying)
  }

  return (
    <div className="space-y-4">
      {/* Sound Info with Play Button */}
      <div className="flex items-center gap-3 p-4  rounded-md border">
        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlay}
          className="h-8 w-8 rounded-full bg-transparent hover:bg-accent text-foreground"
        >
          {
            isPlaying
              ? <Pause className="h-4 w-4" />
              : <Play className="h-4 w-4" />
          }
        </Button>
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium text-foreground truncate">
            {localOverlay.content}
          </p>
        </div>
      </div>

      {/* 音量 */}
      <div className="overlay-setting-card">
        <SoundControls />
      </div>

      {/* 淡入淡出控制 */}
      <div className="overlay-setting-card">
        <AudioFadeControls
          overlay={localOverlay}
          onOverlayChange={updateEditingOverlay}
        />
      </div>

      {/* 变速和时长控制 */}
      <MediaControls />
    </div>
  )
}
