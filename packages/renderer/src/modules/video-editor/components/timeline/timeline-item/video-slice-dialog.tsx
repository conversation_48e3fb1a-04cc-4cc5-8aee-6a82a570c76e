import React, { useEffect, useMemo, useState } from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { FPS } from '@/modules/video-editor/constants'
import { calculateProgressiveOverlayDuration, findOverlayStoryboard } from '@/modules/video-editor/utils/overlay-helper'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { calculateOptimizedSlices } from '@/modules/video-editor/hooks/helpers/useVideoHelper'

interface VideoSliceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  overlay: VideoOverlay
  onConfirm: (sliceDurationInFrames: number) => void
}

type TimeUnit = 'seconds' | 'frames'

export const VideoSliceDialog: React.FC<VideoSliceDialogProps> = ({
  open,
  onOpenChange,
  overlay,
  onConfirm
}) => {
  const { tracks } = useEditorContext()
  const [sliceDuration, setSliceDuration] = useState<number>(0)
  const [timeUnit, setTimeUnit] = useState<TimeUnit>('seconds')
  const [error, setError] = useState<string>('')

  // 获取当前视频所在的分镜
  const storyboard = useMemo(() => {
    return findOverlayStoryboard(tracks, overlay)
  }, [tracks, overlay])

  // 计算实际播放时长
  const actualDurationInFrames = useMemo(() => {
    return calculateProgressiveOverlayDuration(overlay)
  }, [overlay])

  const actualDurationInSeconds = actualDurationInFrames / FPS

  // 计算默认切片时长
  const defaultSliceDuration = useMemo(() => {
    if (storyboard) {
      // 使用分镜时长作为默认值，但不能超过实际播放时长
      return Math.min(storyboard.durationInFrames, actualDurationInFrames)
    }
    // 如果无法获取分镜时长，使用实际播放时长的一半
    return Math.floor(actualDurationInFrames / 2)
  }, [storyboard, actualDurationInFrames])

  // 初始化默认值
  useEffect(() => {
    if (open) {
      const defaultInSeconds = defaultSliceDuration / FPS
      setSliceDuration(Number(defaultInSeconds.toFixed(2)))
      setTimeUnit('seconds')
      setError('')
    }
  }, [open, defaultSliceDuration])

  // 转换为帧数
  const sliceDurationInFrames = useMemo(() => {
    if (timeUnit === 'seconds') {
      return Math.round(sliceDuration * FPS)
    }
    return Math.round(sliceDuration)
  }, [sliceDuration, timeUnit])

  // 验证输入
  useEffect(() => {
    if (sliceDuration <= 0) {
      setError('切片时长必须大于 0')
      return
    }

    const minDurationInFrames = FPS // 1 秒
    if (sliceDurationInFrames < minDurationInFrames) {
      setError('切片时长不能小于 1 秒')
      return
    }

    if (sliceDurationInFrames > actualDurationInFrames) {
      setError('切片时长不能大于视频实际播放时长')
      return
    }

    setError('')
  }, [sliceDuration, sliceDurationInFrames, actualDurationInFrames])

  // 计算预览信息
  const previewInfo = useMemo(() => {
    if (error || sliceDurationInFrames <= 0) {
      return null
    }

    const sliceInfo = calculateOptimizedSlices(actualDurationInFrames, sliceDurationInFrames)

    return {
      sliceCount: sliceInfo.sliceCount,
      lastSliceDurationInSeconds: Number(sliceInfo.lastSliceDurationInSeconds.toFixed(2)),
      isOptimized: sliceInfo.slices.length !== Math.ceil(actualDurationInFrames / sliceDurationInFrames)
    }
  }, [error, sliceDurationInFrames, actualDurationInFrames])

  const handleConfirm = () => {
    if (error || sliceDurationInFrames <= 0) return
    onConfirm(sliceDurationInFrames)
    onOpenChange(false)
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  const handleUnitChange = (newUnit: TimeUnit) => {
    // 转换当前值到新单位
    if (timeUnit === 'seconds' && newUnit === 'frames') {
      setSliceDuration(Math.round(sliceDuration * FPS))
    } else if (timeUnit === 'frames' && newUnit === 'seconds') {
      setSliceDuration(Number((sliceDuration / FPS).toFixed(2)))
    }
    setTimeUnit(newUnit)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>视频切片</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 视频信息 */}
          <div className="p-3 bg-muted rounded-md">
            <div className="text-sm text-muted-foreground mb-1">视频信息</div>
            <div className="text-sm space-y-1">
              {/*<div>原始时长: {originalDurationInSeconds.toFixed(2)} 秒 ({originalDurationInFrames} 帧)</div>*/}
              <div>该视频实际播放时长: {actualDurationInSeconds.toFixed(2)} 秒 ({Math.round(actualDurationInFrames)} 帧)</div>
              {(overlay.trimStartFrames || overlay.trimEndFrames || (overlay.speed && overlay.speed !== 1)) && (
                <div className="text-xs text-muted-foreground">
                  {overlay.trimStartFrames && `去片头: ${(overlay.trimStartFrames / FPS).toFixed(2)}秒 `}
                  {overlay.trimEndFrames && `去片尾: ${(overlay.trimEndFrames / FPS).toFixed(2)}秒 `}
                  {overlay.speed && overlay.speed !== 1 && `播放速度: ${overlay.speed}x`}
                </div>
              )}
              {/*{storyboard && (*/}
              {/*  <div>所在分镜时长: {(storyboard.durationInFrames / FPS).toFixed(2)} 秒</div>*/}
              {/*)}*/}
            </div>
          </div>

          {/* 切片时长输入 */}
          <div className="space-y-2">
            <Label htmlFor="slice-duration">切片时长</Label>
            <div className="flex gap-2">
              <Input
                id="slice-duration"
                type="number"
                value={sliceDuration}
                onChange={e => setSliceDuration(Number(e.target.value))}
                min={0}
                step={timeUnit === 'seconds' ? 0.1 : 1}
                className={error ? 'border-red-500' : ''}
              />
              <Select value={timeUnit} onValueChange={handleUnitChange}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seconds">秒</SelectItem>
                  <SelectItem value="frames">帧</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {error && (
              <div className="text-sm text-red-500">{error}</div>
            )}
          </div>

          {/* 预览信息 */}
          {previewInfo && (
            <div className="p-3 bg-blue-50 dark:bg-blue-950 rounded-md">
              <div className="text-sm text-blue-700 dark:text-blue-300 mb-1">切片预览</div>
              <div className="text-sm text-blue-600 dark:text-blue-400 space-y-1">
                <div>将生成 {previewInfo.sliceCount} 个切片</div>
                {previewInfo.sliceCount > 1 && (
                  <div>最后一个切片时长: {previewInfo.lastSliceDurationInSeconds} 秒</div>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            取消
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!!error || sliceDurationInFrames <= 0}
          >
            确认切片
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
