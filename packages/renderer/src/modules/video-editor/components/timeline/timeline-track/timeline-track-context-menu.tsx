import React, { FC, useMemo } from 'react'
import { OverlayType } from '@clipnest/remotion-shared/types'
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu'
import { ClipboardPaste, Plus } from 'lucide-react'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { isOverlayAcceptableByTrack } from '@/modules/video-editor/utils/track-helper'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'
import { byStartFrame } from '@/modules/video-editor/utils/overlay-helper'
import { useTimelineTrackContext } from './timeline-track-context'
import { TrackType } from '@/modules/video-editor/types'

type TimelineTrackContextMenuProps = React.PropsWithChildren

const AddCaptionMenuItem: FC = () => {
  const { currentTrack } = useTimelineTrackContext()
  const { videoPlayer: { currentFrame } } = useEditorContext()

  const { addOverlayToTrack, generateDefaultCaptionOverlay } = useOverlayHelper()

  const enableToAddTextOverlay = useMemo(() => {
    return !currentTrack.overlays
      .filter(o => o.type === OverlayType.TEXT)
      .some(o => o.from <= currentFrame && o.from + o.durationInFrames >= currentFrame)
  }, [currentTrack, currentFrame])

  if (currentTrack.type !== TrackType.NARRATION) return null

  return (
    <ContextMenuItem
      className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
      disabled={!enableToAddTextOverlay}
      onClick={async e => {
        e.stopPropagation()
        const newOverlay = await generateDefaultCaptionOverlay({ from: currentFrame ?? undefined })
        const nextOverlay = currentTrack.overlays
          .filter(o => o.type === OverlayType.TEXT)
          .sort(byStartFrame()).find(o => o.from > newOverlay.from)

        if (nextOverlay) {
          newOverlay.durationInFrames = Math.min(newOverlay.durationInFrames, nextOverlay.from - newOverlay.from)
        }

        addOverlayToTrack(currentTrack.index, newOverlay)
      }}
    >
      <Plus className="mr-4 h-4 w-4" />
      添加字幕
    </ContextMenuItem>
  )
}

const PasteMenuItem: FC = () => {
  const { currentTrack } = useTimelineTrackContext()
  const { mouseOnCurrentFrame, clipboard } = useTimeline()

  return (
    <ContextMenuItem
      disabled={!clipboard.value || !isOverlayAcceptableByTrack(clipboard.value, currentTrack)}
      className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
      onClick={e => {
        e.stopPropagation()
        if (mouseOnCurrentFrame !== null) {
          clipboard.pasteOverlay(currentTrack.index, mouseOnCurrentFrame)
        }
      }}
    >
      <ClipboardPaste className="mr-4 h-4 w-4" />
      粘贴
    </ContextMenuItem>
  )
}

export const TimelineTrackContextMenu: React.FC<TimelineTrackContextMenuProps> = ({ children }) => {
  const { videoPlayer: { seekTo } } = useEditorContext()
  const { mouseOnCurrentFrame, setIsContextMenuOpen } = useTimeline()

  return (
    <ContextMenu
      onOpenChange={open => {
        if (open && mouseOnCurrentFrame !== null) {
          seekTo(mouseOnCurrentFrame)
        }
        setIsContextMenuOpen(open)
      }}
    >
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className="dark:bg-slate-900 dark:border-slate-800">
        <AddCaptionMenuItem />
        <PasteMenuItem />
      </ContextMenuContent>
    </ContextMenu>
  )
}
