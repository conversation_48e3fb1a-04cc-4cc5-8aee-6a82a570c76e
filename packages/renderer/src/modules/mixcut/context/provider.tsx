import React, { <PERSON>ps<PERSON><PERSON><PERSON><PERSON>dren, useCallback, useMemo, useState } from 'react'
import { Overlay, OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { RenderRequestPayload } from '@app/shared/types/ipc/mixcut'
import { calculateDuration, calculateRenderableOverlays } from '@/modules/video-editor/shared'
import { toast } from 'react-toastify'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { EditorState } from '@/libs/cache/parts/editor.cache'
import { useVirtualTab } from '@/contexts'
import { sleep } from '@app/shared/utils'
import { useQuery } from '@tanstack/react-query'
import { EditorModule } from '@/libs/request/api/editor'
import { DuplicateRateFilter, GeneratedMixcut, MixcutContext, MixcutContextValues, MixcutPageTabs } from './context'
import { IndexableTrack, TrackType } from '@/modules/video-editor/types'
import { useMultiSelection } from './useMultiSelection'
import { generateRenderRequestPayload } from '@/modules/mixcut/utils'
import { getStoryboards } from '@/modules/video-editor/utils/track-helper'

const generateMatrixByTracks = (storyboards: Overlay[], tracks: IndexableTrack[]) => {
  return storyboards
    .map((_, storyboardIndex) => {
      return tracks
        .filter(t => t.overlays.some(o => o.storyboardIndex === storyboardIndex))
        .map(o => o.index)
    })
}

function generateComboFromMatrix(matrix: number[][]): number[] {
  return matrix.map(column =>
    column[Math.floor(Math.random() * column.length)]
  )
}

const useGenerationPart = (state: EditorState, scriptId: string): MixcutContextValues['generation'] => {
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [generateCount, setGenerateCount] = useState(10)
  const [generatedMixcuts, setGeneratedMixcuts] = useState<GeneratedMixcut[]>([])

  const [batchUploadState, setBatchUploadState] = useState<MixcutContextValues['generation']['batchUploadState']>({
    visible: false,
    completed: 0,
    total: 0,
  })

  const [duplicateRateFilter, setDuplicateRateFilter] = useState<DuplicateRateFilter>({
    minRate: 0,
    maxRate: 100
  })

  const multiSelection = useMultiSelection(generatedMixcuts)

  // 批量上传方法
  const uploadSelectedPreviews = useCallback(
    async () => {
      const { selectedIndices, setSelectedIndices } = multiSelection

      const selectedIndicesArray = Array.from(selectedIndices)
      if (selectedIndicesArray.length === 0) {
        toast.warning('请先选择要上传的混剪结果')
        return
      }

      setBatchUploadState({
        visible: true,
        completed: 0,
        total: selectedIndicesArray.length,
      })

      try {
        for (let i = 0; i < selectedIndicesArray.length; i++) {
          const index = selectedIndicesArray[i]
          const mixcut = generatedMixcuts[index]

          setBatchUploadState(prev => ({
            ...prev,
            currentItem: `组合 [${mixcut.videoCombo.selections.join(', ')}]`
          }))

          // 获取第一个视频overlay用于封面
          const { tracks, playerMetadata } = state
          const firstStoryboardTrackIndex = mixcut.videoCombo.selections[0]
          const targetVideoTrack = tracks[firstStoryboardTrackIndex]
          const firstVideoOverlay = targetVideoTrack?.overlays.find(
            (overlay: any) => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
          ) as VideoOverlay | null

          const data: RenderRequestPayload = generateRenderRequestPayload(tracks, mixcut, playerMetadata)

          await Promise.all([
            window.mixcut.uploadMixcutResult({
              scriptId,
              data,
              similarity: mixcut.videoCombo.similarity,
              cover: firstVideoOverlay?.originalMeta.coverUrl,
              duration: calculateDuration(data.inputProps.overlays)
            }),
            new Promise(resolve => setTimeout(resolve, 1500))
          ])

          setBatchUploadState(prev => ({
            ...prev,
            completed: i + 1
          }))
        }

        toast.success(`成功保存 ${selectedIndicesArray.length} 个混剪结果！`)

        setGeneratedMixcuts(prev => {
          return prev.filter((_, index) => !selectedIndicesArray.includes(index))
        })
        setSelectedIndices(new Set())

        void queryClient.refetchQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST] })
        await sleep(1000)
      } catch (error) {
        console.error('批量上传失败:', error)
        toast.error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
      } finally {
        setBatchUploadState({
          visible: false,
          completed: 0,
          total: 0,
        })
      }
    },
    [multiSelection, generatedMixcuts, state]
  )

  const generateCombinations = useCallback(async () => {
    const { tracks } = state

    const { videoTracks, narrationTracks } = tracks.reduce(
      (result, track, trackIndex) => {
        if (!track.isGlobalTrack && track.type === TrackType.VIDEO) {
          result.videoTracks.push({ ...track, index: trackIndex })
        }
        if (!track.isGlobalTrack && track.type === TrackType.NARRATION) {
          result.narrationTracks.push({ ...track, index: trackIndex })
        }

        return result
      },
      {
        videoTracks: [] as IndexableTrack[],
        narrationTracks: [] as IndexableTrack[],
      }
    )

    const storyboards = getStoryboards(tracks)
    const videoMatrix = generateMatrixByTracks(storyboards, videoTracks)
    const narrationMatrix = generateMatrixByTracks(storyboards, narrationTracks)

    const combos = await window.mixcut.generateCombos({
      limit: generateCount,
      threshold: 1,
      matrix: videoMatrix,
    })

    setGeneratedMixcuts(
      combos.map(combo => ({
        videoCombo: combo,
        narrationSelections: generateComboFromMatrix(narrationMatrix)
      }))
    )

    // 清空选择状态
    multiSelection.setSelectedIndices(new Set())
    setDrawerOpen(false)
  }, [state.tracks, generateCount])

  return {
    drawerOpen,
    setDrawerOpen,

    generateCount,
    setGenerateCount,

    generatedMixcuts,
    batchUploadState,
    uploadSelectedPreviews,
    generateCombinations,

    duplicateRateFilter,
    setDuplicateRateFilter,

    ...multiSelection,
  }
}

const useSavedPart = (_state: EditorState, scriptId: string) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  const multiSelection = useMultiSelection(data?.list || [])

  return {
    ...multiSelection,
  }
}

export const MixcutProvider: React.FC<PropsWithChildren<{ state: EditorState, defaultTab?: MixcutPageTabs }>> = ({
  children, state, defaultTab
}) => {
  const { params } = useVirtualTab('Mixcut')
  const scriptId = params?.scriptId

  const [activeTab, setActiveTab] = useState<MixcutPageTabs>(defaultTab || 'generation')

  const generationPart = useGenerationPart(state, scriptId)
  const savedPart = useSavedPart(state, scriptId)

  const playerOverlays = useMemo(() => {
    if (activeTab === 'generation' && generationPart.activeItem) {
      return calculateRenderableOverlays(state.tracks, {
        [TrackType.VIDEO]: generationPart.activeItem.videoCombo.selections,
        [TrackType.NARRATION]: generationPart.activeItem.narrationSelections,
      })
    }

    return []
  }, [activeTab, state.tracks, generationPart.activeItem, savedPart.activeItem])

  return (
    <MixcutContext.Provider
      value={{
        state,

        generation: generationPart,
        saved: savedPart,

        playerOverlays,
        activeTab,
        setActiveTab,

        rules: {
          material: {
            enableShuffle: false,
            enableSmartTrim: false
          },
          deduplicate: {
            enableDeduplicate: false,
            operationStatus: new Map()
          }
        },
      }}
    >
      {children}
    </MixcutContext.Provider>
  )
}
