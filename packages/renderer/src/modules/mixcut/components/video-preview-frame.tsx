import React from 'react'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import { AuthedImg } from '@/components/authed-img'

export const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  const fallback = (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center">
      <div className="text-white/60 text-sm">无预览</div>
    </div>
  )

  if (!overlay?.originalMeta) return fallback

  const { coverUrl, height, width } = overlay.originalMeta

  if (!coverUrl) {
    return fallback
  }

  const aspectRatio = width && height ? width / height : 1
  
  return (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center relative overflow-hidden rounded-sm">
      <AuthedImg src={overlay.originalMeta.coverUrl} style={{ aspectRatio }} />
    </div>
  )
}
