import React, { useCallback, useState } from 'react'
import { toast } from 'react-toastify'
import { OssModule } from '@/libs/request/api/oss'
import { RenderRequestPayload } from '@app/shared/types/ipc/mixcut'
import { Mixcut } from '@/types/mixcut'
import { EditorModule } from '@/libs/request/api/editor'
import { Button } from '@/components/ui/button'
import { Download } from 'lucide-react'
import { useMixcutContext } from '../context/context'
import { extractObjectIdFromObjectHref } from '@/libs/tools/resource'
import { TeamManager, TokenManager } from '@/libs/storage'
import { RenderTask } from '@app/shared/types/render-task.types'
import { useTaskCenter } from '@/modules/task-center/context/context'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { createSearchParams, useNavigate } from 'react-router'
import { useVirtualTab } from '@/contexts'

// 导出进度遮罩组件
const ExportProgressOverlay: React.FC<{
  exportState: {
    visible: boolean
    completed: number
    total: number
    currentItem: string
  }
}> = ({ exportState }) => {
  const { visible, completed, total, currentItem } = exportState

  if (!visible) return null

  const progress = total > 0 ? (completed / total) * 100 : 0

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-background rounded-lg p-8 shadow-lg border max-w-sm w-full mx-4">
        <div className="flex flex-col items-center space-y-6">
          {/* 圆环进度条 */}
          <div className="relative w-32 h-32">
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              {/* 背景圆环 */}
              <circle
                cx="60"
                cy="60"
                r="54"
                stroke="oklch(0.551 0.027 264.364)"
                strokeWidth="8"
                fill="transparent"
              />
              {/* 进度圆环 */}
              <circle
                cx="60"
                cy="60"
                r="54"
                stroke="oklch(0.715 0.143 215.221)"
                strokeWidth="8"
                fill="transparent"
                strokeDasharray={`${2 * Math.PI * 54}`}
                strokeDashoffset={`${2 * Math.PI * 54 * (1 - progress / 100)}`}
                className="transition-all duration-300 ease-in-out"
              />
            </svg>
            {/* 中心文字 */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <div className="text-2xl font-bold text-foreground">
                {completed}
              </div>
              <div className="text-sm text-muted-foreground">
                / {total}
              </div>
            </div>
          </div>

          {/* 状态文本 */}
          <div className="text-center space-y-2">
            <h3 className="text-lg font-medium text-foreground">
              正在导出混剪
            </h3>
            {currentItem && (
              <p className="text-sm text-muted-foreground">
                当前: {currentItem}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// 一键导出所选混剪按钮组件
export const SavedMixcutExportButton = () => {
  const { saved } = useMixcutContext()
  const { createTask } = useTaskCenter().rendering

  const { params } = useVirtualTab('Mixcut')
  const scriptId = params.scriptId
  const projectId = params.projectId

  const { goToHomePage } = useVirtualTabsStore()
  const navigte = useNavigate()

  const [exportState, setExportState] = useState({
    visible: false,
    completed: 0,
    total: 0,
    currentItem: ''
  })

  // 从 URL 中提取 objectId
  const extractObjectId = useCallback((url: string): string | null => {
    const match = url.match(/oss\/object-href\/(\w+)$/)
    return match ? match[1] : null
  }, [])

  const handleNavigateToWorkspace = () => {
    goToHomePage()
    navigte({
      pathname: `/home/<USER>/${projectId}/works`,
      search: `?${createSearchParams({ scriptId })}`
    })
  }

  const handleExportSelected = useCallback(async () => {
    const selectedIndicesArray = Array.from(saved.selectedIndices)
    if (selectedIndicesArray.length === 0) {
      toast.warning('请先选择要导出的混剪')
      return
    }

    if (!saved.list) {
      toast.error('获取混剪列表失败')
      return
    }

    try {
      const selectedMixcuts = selectedIndicesArray.map(index => saved.list[index])

      // 显示进度遮罩
      setExportState({
        visible: true,
        completed: 0,
        total: selectedMixcuts.length,
        currentItem: ''
      })

      // 逐个处理导出
      for (let i = 0; i < selectedMixcuts.length; i++) {
        const mixcut = selectedMixcuts[i]

        const coverObjectId = extractObjectIdFromObjectHref(mixcut.cover) || ''
        if (!coverObjectId) {
          toast.error(`无法从混剪[${i}]中提取有效封面图片`)
          continue
        }

        setExportState(prev => ({
          ...prev,
          currentItem: mixcut.name,
          completed: i
        }))

        // 从 URL 中提取 objectId
        const objectId = extractObjectId(mixcut.url)
        if (!objectId) {
          console.error(`无法从 URL 中提取 objectId: ${mixcut.url}`)
          continue
        }

        const metadata = await OssModule.getObject<RenderRequestPayload>(objectId)

        const { inputProps: { playerMetadata: { width, height, fps, durationInFrames } } } = metadata

        // 构建 RequestRender 参数
        const renderParams: Mixcut.RequestRender = {
          objectId,
          scriptId: mixcut.scriptId,
          name: mixcut.name,
          coverObjectId,
          resolution: `${width}x${height}`,
          fps,
          duration: durationInFrames * fps,

          bitRate: 0,
          createAt: 0,
          display: 0,
          isSaveList: false,
          itemId: '1',
          previewId: 0,
          priority: 0,
          product: 0,
          repetitionRate: 0,
        }

        // 调用渲染接口
        const taskNo = await EditorModule.requestRender(renderParams)

        // 保存渲染任务到本地存储
        try {
          const currentUser = TokenManager.getUserId()
          const currentTeam = TeamManager.current()

          if (currentUser && taskNo) {
            const renderTaskParams: RenderTask.CreateParams = {
              uid: String(currentUser),
              team_id: currentTeam || 0,
              task_no: taskNo,
              name: mixcut.name,
              cover_url: mixcut.cover,
              cover_object_id: coverObjectId,
              script_id: mixcut.scriptId,
              object_id: objectId,
              resolution: `${width}x${height}`,
              fps,
              duration: durationInFrames / fps,
              status: RenderTask.RenderTaskStatus.WAITING,
              progress: 0
            }

            await createTask(renderTaskParams)
            console.log(`渲染任务已保存到本地: ${taskNo}`)
          }
        } catch (error) {
          console.error('保存渲染任务到本地失败:', error)
          // 不影响主流程，只记录错误
        }
      }

      // 完成所有导出
      setExportState(prev => ({
        ...prev,
        completed: selectedMixcuts.length
      }))

      toast.success(`成功提交 ${selectedMixcuts.length} 个混剪的导出任务`)

      // 延迟隐藏进度遮罩
      setTimeout(() => {
        setExportState({
          visible: false,
          completed: 0,
          total: 0,
          currentItem: ''
        })
      }, 1500)

      handleNavigateToWorkspace()
    } catch (error) {
      console.error('导出混剪失败:', error)
      toast.error('导出混剪失败，请重试')
      setExportState({
        visible: false,
        completed: 0,
        total: 0,
        currentItem: ''
      })
    }
  }, [saved, extractObjectId])

  if (!saved.selectedIndices.size) return null

  return (
    <>
      <Button
        className="bg-gradient-brand"
        size="sm"
        onClick={handleExportSelected}
      >
        <Download className="w-4 h-4 mr-2" />
        一键导出所选混剪 ({saved.selectedIndices.size})
      </Button>

      {/* 导出进度遮罩 */}
      {exportState.visible && (
        <ExportProgressOverlay exportState={exportState} />
      )}
    </>
  )
}
