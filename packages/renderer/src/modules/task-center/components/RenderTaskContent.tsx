import React, { useCallback, useState } from 'react'
import { Download, Loader2, Play, Trash2 } from 'lucide-react'
import { RenderTask } from '@app/shared/types/render-task.types'
import { cn } from '@/components/lib/utils'
import { Button } from '@/components/ui/button'
import { useTaskCenter } from '../context/context'
import { throttle } from 'lodash'
import { AuthedImg } from '@/components/authed-img'

/**
 * 渲染任务内容组件
 */
export const RenderTaskContent = () => {
  const [taskOperations, setTaskOperations] = useState<Record<number, string | null>>({})

  const {
    tasks,
    deleteTask,
    downloadTask,
    getStatusText,
    getStatusColor,
    formatFileSize,
    formatDuration,
  } = useTaskCenter().rendering

  // 处理任务操作
  const handleTaskAction = useCallback(
    throttle(
      async (task: RenderTask.IRenderTask, action: string) => {
        // 检查是否已经在执行操作
        if (taskOperations[task.id]) {
          return
        }

        try {
          // 设置操作状态
          setTaskOperations(prev => ({ ...prev, [task.id]: action }))

          switch (action) {
            case 'download':
              await downloadTask(task)
              break
            case 'delete':
              await deleteTask(task.id)
              break
            default:
              console.warn(`未知操作: ${action}`)
          }
        } catch (error) {
          console.error(`[RenderTaskManager] 任务 ${task.id} 的 ${action} 操作失败:`, error)
        } finally {
          // 清除操作状态
          setTaskOperations(prev => {
            const newState = { ...prev }
            delete newState[task.id]
            return newState
          })
        }
      },
      500
    ),
    [taskOperations, deleteTask, downloadTask]
  )

  // const completedCount = tasks.filter(task => task.status === RenderTask.RenderTaskStatus.COMPLETED).length
  // const totalCount = tasks.length

  if (tasks.length === 0) {
    return (
      <div className="text-sm flex items-center justify-center py-6 text-muted">
        暂无渲染任务
      </div>
    )
  }

  return (
    <div className="relative">
      <div className="max-h-[60vh] overflow-y-auto">
        <div className="flex flex-col gap-2">
          {tasks.map(task => (
            <div key={task.id} className="flex items-center gap-3 p-2 bg-neutral-900 rounded-lg">
              {/* 封面图 */}
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded flex items-center justify-center overflow-hidden">
                {task.cover_url ? (
                  <AuthedImg
                    src={task.cover_url}
                    alt={task.name}
                    className="w-full h-full object-cover rounded"
                  />
                ) : (
                  <Play className="w-6 h-6 text-white" />
                )}
              </div>

              {/* 任务信息 */}
              <div className="flex-1 min-w-0">
                <div className="text-sm truncate" title={task.name}>
                  {task.name}
                </div>
                <div className="flex items-center gap-4">
                  {/* 文件大小 */}
                  {task.status === RenderTask.RenderTaskStatus.COMPLETED && task.file_size > 0 && (
                    <div className="text-xs text-muted">
                      {formatFileSize(task.file_size)}
                    </div>
                  )}

                  {/* 时长 */}
                  {task.duration > 0 && (
                    <div className="text-xs text-muted">
                      {formatDuration(task.duration)}
                    </div>
                  )}

                  {/* 状态徽章 */}
                  <div className={cn(getStatusColor(task.status), 'text-xs')}>
                    {getStatusText(task.status)}
                  </div>
                </div>

                {/* 进度条和详细信息 */}
                <div className="text-[10px] mt-1">
                  {task.status === RenderTask.RenderTaskStatus.RENDERING && (
                    <div className="flex items-center gap-2">
                      <div className="flex-1 h-1 bg-gray-200 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-blue-500 transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        />
                      </div>
                      <span>{task.progress.toFixed(1)}%</span>
                    </div>
                  )}

                  {task.reason && (
                    <div className="text-red-400 mt-1">
                      {task.reason}
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2 ml-4">
                {/* 下载按钮 - 仅在渲染完成且有下载链接时显示 */}
                {task.status === RenderTask.RenderTaskStatus.COMPLETED && task.download_url && (
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => handleTaskAction(task, 'download')}
                    disabled={!!taskOperations[task.id]}
                    title="下载视频"
                  >
                    {taskOperations[task.id] === 'download' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Download className="w-4 h-4" />
                    )}
                  </Button>
                )}

                {/* 重试按钮 - 仅在失败时显示 */}
                {/*{task.status === RenderTask.Status.FAILED && (*/}
                {/*  <Button*/}
                {/*    size="icon"*/}
                {/*    variant="outline"*/}
                {/*    onClick={() => handleTaskAction(task, 'retry')}*/}
                {/*    disabled={!!taskOperations[task.id]}*/}
                {/*    title="重试渲染"*/}
                {/*  >*/}
                {/*    {taskOperations[task.id] === 'retry' ? (*/}
                {/*      <Loader2 className="w-4 h-4 animate-spin" />*/}
                {/*    ) : (*/}
                {/*      <RotateCcw className="w-4 h-4" />*/}
                {/*    )}*/}
                {/*  </Button>*/}
                {/*)}*/}

                {/* 删除按钮 */}
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => handleTaskAction(task, 'delete')}
                  disabled={!!taskOperations[task.id]}
                  title="删除任务"
                >
                  {taskOperations[task.id] === 'delete' ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部统计信息 */}
      {/*{tasks.length > 0 && (*/}
      {/*  <div className="p-4 border-t bg-neutral-600">*/}
      {/*    <div className="flex items-center justify-between text-sm">*/}
      {/*      <span className="text-green-600 font-medium">*/}
      {/*        完成 {completedCount}/{totalCount}*/}
      {/*      </span>*/}
      {/*      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">*/}
      {/*        <div*/}
      {/*          className="h-full bg-green-500 transition-all duration-300"*/}
      {/*          style={{ width: `${totalCount > 0 ? (completedCount / totalCount) * 100 : 0}%` }}*/}
      {/*        />*/}
      {/*      </div>*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/*)}*/}
    </div>
  )
}
