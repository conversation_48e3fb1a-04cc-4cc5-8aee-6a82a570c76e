import React, { useCallback } from 'react'
import { SettingsTabs, TabItem } from '@/modules/video-editor/shared'
import { UploadTask } from '@app/shared/types/upload-task.types'
import { Button } from '@/components/ui/button'
import { useTaskCenter } from '@/modules/task-center/context/context'
import { RenderTaskContent } from './RenderTaskContent'
import { UploadTaskContent } from '@/modules/task-center/components/UploadTaskContent'

interface TaskCenterProps {
  onClose?: () => void
}

/**
 * 任务中心组件
 */
export const TaskCenter: React.FC<TaskCenterProps> = () => {
  const {
    tasks,
    retryUpload,
    cleanupCompleted
  } = useTaskCenter().uploading

  // 清空记录
  const handleClearRecords = useCallback(async () => {
    try {
      await cleanupCompleted()
    } catch (error) {
      console.error('清空记录失败:', error)
    }
  }, [cleanupCompleted])

  // 重试失败的任务
  const handleRetryAll = useCallback(async () => {
    const failedTasks = tasks.filter(task => task.status === UploadTask.Status.FAILED)
    for (const task of failedTasks) {
      try {
        await retryUpload(task.id)
      } catch (error) {
        console.error(`重试任务 ${task.id} 失败:`, error)
      }
    }
  }, [tasks, retryUpload])

  const TABS: TabItem[] = [
    {
      value: 'upload',
      label: '上传记录',
      content: <UploadTaskContent />
    },
    {
      value: 'render',
      label: '渲染记录',
      content: <RenderTaskContent />
    }
  ]

  return (
    <div className="w-120 bg-neutral-800 backdrop-blur-lg  rounded-lg  flex flex-col px-4 py-3">
      <SettingsTabs tabs={TABS} defaultTab="upload" />

      {/* 头部 */}
      <div className="flex items-center justify-between p-4 border-b">

        <div className="flex gap-2 text-sm">
          <Button size="sm" variant="link" onClick={handleClearRecords} >
            清空记录
          </Button>
          <Button size="sm" variant="link" onClick={handleRetryAll} >
            重试
          </Button>
        </div>
      </div>
    </div>
  )
}
