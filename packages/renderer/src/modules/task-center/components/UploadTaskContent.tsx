import { UploadTask } from '@app/shared/types/upload-task.types'
import React, { useCallback, useState } from 'react'
import { useTaskCenter } from '@/modules/task-center/context/context'
import { cn } from '@/components/lib/utils'
import { Button } from '@/components/ui/button'
import { Loader2, Pause, Play, RotateCcw, Square, Trash2 } from 'lucide-react'
import { round } from 'lodash'

const getStatusText = (status: UploadTask.Status) => {
  const statusMap = {
    [UploadTask.Status.PENDING]: '等待上传',
    [UploadTask.Status.UPLOADING]: '上传中',
    [UploadTask.Status.PAUSED]: '已暂停',
    [UploadTask.Status.COMPLETED]: '上传完成',
    [UploadTask.Status.FAILED]: '上传失败',
    [UploadTask.Status.CANCELLED]: '已取消'
  }
  return statusMap[status] || '未知状态'
}

const getStatusColor = (status: UploadTask.Status) => {
  switch (status) {
    case UploadTask.Status.PENDING:
      return 'text-blue-500'
    case UploadTask.Status.UPLOADING:
      return 'text-blue-500'
    case UploadTask.Status.PAUSED:
      return 'text-orange-400'
    case UploadTask.Status.COMPLETED:
      return 'text-green-500'
    case UploadTask.Status.FAILED:
      return 'text-red-500'
    case UploadTask.Status.CANCELLED:
      return 'text-gray-400'
    default:
      return 'text-blue-500'
  }
}

const formatFileSize = (bytes: number) => {
  if (!isFinite(bytes) || bytes <= 0) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))

  return `${round(bytes / Math.pow(1024, i), 2)} ${units[i]}`
}

export const UploadTaskContent = () => {
  const [lastOperationTime, setLastOperationTime] = useState<Record<number, number>>({})
  const [taskOperations, setTaskOperations] = useState<Record<number, string | null>>({})

  const {
    tasks,
    startUpload,
    pauseUpload,
    resumeUpload,
    cancelUpload,
    retryUpload,
    deleteTask,
  } = useTaskCenter().uploading

  // 处理任务操作
  const handleTaskAction = useCallback(async (taskId: number, action: string) => {
    const now = Date.now()
    const lastTime = lastOperationTime[taskId] || 0

    // 减少防抖时间：200ms 内不允许重复操作（避免误触）
    if (now - lastTime < 200) {
      console.log(`任务 ${taskId} 操作过于频繁，忽略重复点击`)
      return
    }

    // 检查是否已经在执行操作
    if (taskOperations[taskId]) {
      console.log(`任务 ${taskId} 正在执行 ${taskOperations[taskId]} 操作，忽略重复点击`)
      return
    }

    try {
      // 记录操作时间
      setLastOperationTime(prev => ({ ...prev, [taskId]: now }))
      // 设置操作状态
      setTaskOperations(prev => ({ ...prev, [taskId]: action }))

      console.log(`[UploadTaskManager] 执行任务 ${taskId} 的 ${action} 操作`)

      switch (action) {
        case 'start':
          await startUpload(taskId)
          break
        case 'pause':
          await pauseUpload(taskId)
          break
        case 'resume':
          await resumeUpload(taskId)
          break
        case 'cancel':
          await cancelUpload(taskId)
          break
        case 'retry':
          await retryUpload(taskId)
          break
        case 'delete':
          await deleteTask(taskId)
          break
      }

      console.log(`[UploadTaskManager] 任务 ${taskId} 的 ${action} 操作完成`)
    } catch (error) {
      console.error(`[UploadTaskManager] 任务 ${taskId} 的 ${action} 操作失败:`, error)
    } finally {
      // 清除操作状态
      setTaskOperations(prev => {
        const newState = { ...prev }
        delete newState[taskId]
        return newState
      })
    }
  }, [taskOperations, lastOperationTime, startUpload, pauseUpload, resumeUpload, cancelUpload, retryUpload, deleteTask])

  const completedCount = tasks.filter(task => task.status === UploadTask.Status.COMPLETED).length
  const totalCount = tasks.length

  if (tasks.length === 0) {
    return (
      <div className="text-sm flex items-center justify-center py-6 text-muted">
        暂无数据
      </div>
    )
  }

  return (
    <div className="relative">
      <div className="max-h-[60vh] overflow-y-auto">
        <div className="flex flex-col gap-2">
          {
            tasks.map(task => (
              <div key={task.id} className="flex items-center gap-3 p-2 bg-neutral-900 rounded-lg">
                {/* 缩略图 */}
                <div
                  className="w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded flex items-center justify-center"
                >
                  {
                    task.type === UploadTask.Type.IMAGE && (
                      <img src={task.local_path} alt="" className="w-full h-full object-cover rounded" />
                    )
                  }
                </div>

                {/* 任务信息 */}
                <div className="flex-1 min-w-0">
                  <div className="text-sm truncate">{task.name}</div>
                  <div className="flex items-center gap-4">
                    <div className="text-xs text-muted">
                      {formatFileSize(task.size || 0)}
                    </div>
                    {/* 状态徽章 */}
                    <div className={cn(getStatusColor(task.status), 'text-xs')}>
                      {getStatusText(task.status)}
                    </div>
                  </div>
                  <div className="text-[10px] mt-1">
                    {(task.progress * 100).toFixed(1)}% - {task.reason && `${task.reason}`}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-2 ml-4">
                  {/* 操作按钮 */}
                  {task.status === UploadTask.Status.PENDING && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleTaskAction(task.id, 'start')}
                      disabled={!!taskOperations[task.id]}
                    >
                      {taskOperations[task.id] === 'start' ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  )}

                  {(task.status === UploadTask.Status.UPLOADING ||
                    (task.status === UploadTask.Status.PENDING && task.progress > 0)) && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleTaskAction(task.id, 'pause')}
                      disabled={!!taskOperations[task.id]}
                    >
                      {taskOperations[task.id] === 'pause' ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Pause className="w-4 h-4" />
                      )}
                    </Button>
                  )}

                  {task.status === UploadTask.Status.PAUSED && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleTaskAction(task.id, 'resume')}
                      disabled={!!taskOperations[task.id]}
                    >
                      {taskOperations[task.id] === 'resume' ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Play className="w-4 h-4" />
                      )}
                    </Button>
                  )}

                  {task.status === UploadTask.Status.FAILED && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleTaskAction(task.id, 'retry')}
                      disabled={!!taskOperations[task.id]}
                    >
                      {taskOperations[task.id] === 'retry' ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <RotateCcw className="w-4 h-4" />
                      )}
                    </Button>
                  )}

                  {(task.status === UploadTask.Status.PENDING ||
                    task.status === UploadTask.Status.UPLOADING ||
                    task.status === UploadTask.Status.PAUSED) && (
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => handleTaskAction(task.id, 'cancel')}
                      disabled={!!taskOperations[task.id]}
                    >
                      {taskOperations[task.id] === 'cancel' ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        <Square className="w-4 h-4" />
                      )}
                    </Button>
                  )}

                  <Button
                    size="icon"
                    variant="outline"
                    onClick={() => handleTaskAction(task.id, 'delete')}
                    disabled={!!taskOperations[task.id]}
                  >
                    {taskOperations[task.id] === 'delete' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash2 className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>
            ))
          }
        </div>

      </div>

      {
        tasks.length > 0 && (
          <div className="p-4 border-t bg-neutral-600">
            <div className="flex items-center justify-between text-sm">
              <span className="text-green-600 font-medium">
                完成 {completedCount}/{totalCount}
              </span>
              <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 transition-all duration-300"
                  style={{ width: `${totalCount > 0 ? (completedCount / totalCount) * 100 : 0}%` }}
                />
              </div>
            </div>
          </div>
        )
      }
    </div>
  )
}
