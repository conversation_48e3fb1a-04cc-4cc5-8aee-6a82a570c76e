import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { RenderTask } from '@app/shared/types/render-task.types'
import { TeamManager, TokenManager } from '@/libs/storage'
import { toast } from 'react-toastify'
import { RenderTaskIPCClient } from '@app/shared/types/ipc/crudables/render-task'

export type RenderTaskHook = {
  tasks: any[];
  stats: null | RenderTask.TaskStats | undefined;
  isLoading: false | true | boolean;
  createTask: RenderTaskIPCClient['create']
  deleteTask: RenderTaskIPCClient['delete']
  downloadTask: (task: RenderTask.IRenderTask) => Promise<void>;
  getStatusText: (status: RenderTask.RenderTaskStatus) => string;
  getStatusColor: (status: RenderTask.RenderTaskStatus) => (string);
  formatFileSize: (bytes: number) => string;
  formatDuration: (seconds: number) => string;
}

/**
 * 渲染任务管理Hook
 */
export function useRenderTasks(): RenderTaskHook {
  const queryClient = useQueryClient()
  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 获取用户渲染任务
  const { data: tasks = [], isLoading } = useQuery({
    queryKey: ['renderTasks', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return []
      return window.renderTask.getUserTasks({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 3000 // 10秒刷新一次
  })

  // 获取渲染任务统计
  const { data: stats } = useQuery({
    queryKey: ['renderTaskStats', currentUser, currentTeam],
    queryFn: async () => {
      if (!currentUser) return null
      return window.renderTask.getTaskStats({
        uid: String(currentUser),
        teamId: currentTeam
      })
    },
    enabled: !!currentUser,
    refetchInterval: 15000 // 15秒刷新一次
  })

  // 创建渲染任务
  const createTaskMutation = useMutation({
    mutationFn: async (params: RenderTask.CreateParams) => {
      return window.renderTask.create(params)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['renderTasks'] })
      queryClient.invalidateQueries({ queryKey: ['renderTaskStats'] })
    }
  })

  // 删除渲染任务
  const deleteTaskMutation = useMutation({
    mutationFn: async (taskId: number) => {
      return window.renderTask.delete(taskId)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['renderTasks'] })
      queryClient.invalidateQueries({ queryKey: ['renderTaskStats'] })
      toast.success('任务已删除')
    },
    onError: error => {
      console.error('删除渲染任务失败:', error)
      toast.error('删除任务失败')
    }
  })

  // 下载渲染结果
  const downloadTask = async (task: RenderTask.IRenderTask) => {
    if (!task.download_url) {
      toast.warning('下载链接不可用')
      return
    }

    try {
      // 使用浏览器下载
      const link = document.createElement('a')
      link.href = task.download_url
      link.download = `${task.name}.mp4`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast.success('开始下载')
    } catch (error) {
      console.error('下载失败:', error)
      toast.error('下载失败')
    }
  }

  // 获取任务状态文本
  const getStatusText = (status: RenderTask.RenderTaskStatus) => {
    const statusMap = {
      [RenderTask.RenderTaskStatus.WAITING]: '等待渲染',
      [RenderTask.RenderTaskStatus.RENDERING]: '渲染中',
      [RenderTask.RenderTaskStatus.COMPLETED]: '渲染完成',
      [RenderTask.RenderTaskStatus.FAILED]: '渲染失败',
      [RenderTask.RenderTaskStatus.CANCELED]: '已取消'
    }
    return statusMap[status] || '未知状态'
  }

  // 获取任务状态颜色
  const getStatusColor = (status: RenderTask.RenderTaskStatus) => {
    switch (status) {
      case RenderTask.RenderTaskStatus.WAITING:
        return 'text-blue-500'
      case RenderTask.RenderTaskStatus.RENDERING:
        return 'text-blue-500'
      case RenderTask.RenderTaskStatus.COMPLETED:
        return 'text-green-500'
      case RenderTask.RenderTaskStatus.FAILED:
        return 'text-red-500'
      case RenderTask.RenderTaskStatus.CANCELED:
        return 'text-gray-400'
      default:
        return 'text-blue-500'
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (!isFinite(bytes) || bytes <= 0) return '0 B'

    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))

    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`
  }

  // 格式化时长
  const formatDuration = (seconds: number) => {
    if (!isFinite(seconds) || seconds <= 0) return '0:00'

    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)

    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return {
    // 数据
    tasks,
    stats,
    isLoading,

    // 操作方法
    createTask: createTaskMutation.mutateAsync,
    deleteTask: deleteTaskMutation.mutateAsync,
    downloadTask,

    // 工具方法
    getStatusText,
    getStatusColor,
    formatFileSize,
    formatDuration,
  }
}
