import { useUploadTasks } from '@/modules/task-center/useUploadTasks'
import React from 'react'

export type UploadTasksContextValue = ReturnType<typeof useUploadTasks>

export const UploadTasksContext = React.createContext<UploadTasksContextValue>(null as any)

export const useUploadTasksContext = () => {
  const ctx = React.useContext(UploadTasksContext)
  if (!ctx) {
    throw new Error('useUploadTasks must be used within an UploadTasksProvider')
  }
  return ctx
}
