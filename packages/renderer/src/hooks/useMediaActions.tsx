// hooks/useMediaActions.ts
import React, { useMemo } from 'react'
import { Edit, FolderInput, Trash } from 'lucide-react'
import { ResourceSource, FolderActionKeys, actionLabels } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'
import { MediaAction } from '@/components/material/MediaItem'

export const useMediaActions = (
  isLocal: boolean,
  type: ResourceSource,
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  const { renameItem, deleteItem, deleteLocalItem } = useItemActions()

  return useMemo<MediaAction[]>(
    () => [
      {
        icon: <Edit className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.RENAME],
        value: FolderActionKeys.RENAME,
        onClick: (fileId, fileName) =>
          renameItem(type, fileId, fileName, {
            label: '素材名称',
            headerTitle: '素材',
          }),
      },
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.MOVE],
        value: FolderActionKeys.MOVE,
        onClick: fileId => {
          setMoveType(type)
          setMoveId(fileId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DELETE],
        value: FolderActionKeys.DELETE,
        onClick: (fileId, fileName) => {
          if (isLocal) {
            return deleteLocalItem(type, fileId, fileName)
          }
          return deleteItem(type, fileId, fileName)
        },
      },
    ],
    [renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen, type, isLocal, deleteLocalItem],
  )
}
