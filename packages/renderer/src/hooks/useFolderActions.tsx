import React, { useMemo } from 'react'
import { Edit, FolderInput, FolderPlus, Trash, SquareMenu } from 'lucide-react'
import { ResourceSource, FolderActionKeys, actionLabels } from '@/types/resources'
import { useItemActions } from '@/hooks/useItemActions'
import { TreeNode } from '@/components/TreeList'
import { FolderAction } from '@/components/material/MediaItem'

export const useFolderActions = (
  isLocal: boolean,
  type: ResourceSource,
  childFolders: TreeNode[],
  setMoveType: (type: ResourceSource) => void,
  setMoveId: (id: string) => void,
  setMoveDialogOpen: (open: boolean) => void,
) => {
  const { createItem, renameItem, deleteItem, deleteLocalItem } = useItemActions()

  return useMemo<FolderAction[]>(() => {
    const actions: FolderAction[] = [
      {
        icon: <FolderInput className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.MOVE],
        value: FolderActionKeys.MOVE,
        onClick: nodeId => {
          setMoveType(type)
          setMoveId(nodeId)
          setMoveDialogOpen(true)
        },
      },
      {
        icon: <Edit className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.RENAME],
        value: FolderActionKeys.RENAME,
        onClick: (nodeId, _parentId, label) =>
          renameItem(type, nodeId, label!, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }, true, childFolders),
      },
      {
        icon: <Trash className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DELETE],
        value: FolderActionKeys.DELETE,
        onClick: (nodeId, _parentId, label) => {
          if (isLocal) {
            return deleteLocalItem(type, nodeId, label!)
          }
          return deleteItem(type, nodeId, label!)
        },
      },
    ]
    if (!isLocal) {
      actions.unshift({
        icon: <FolderPlus className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.CREATE],
        value: FolderActionKeys.CREATE,
        onClick: nodeId =>
          createItem(type, nodeId, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          }, childFolders),
      })
      actions.splice(actions.length - 1, 0, {
        icon: <SquareMenu className="w-4 h-4" />,
        label: actionLabels[FolderActionKeys.DETAILS],
        value: FolderActionKeys.DETAILS,
        onClick: nodeId =>
          console.log('查看文件夹详细信息', nodeId)
      })
    }
    return actions
  }, [createItem, renameItem, deleteItem, deleteLocalItem, setMoveType, setMoveId, setMoveDialogOpen, isLocal])
}
