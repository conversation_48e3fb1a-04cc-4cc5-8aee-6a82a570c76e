import {  PublishMode, Setting, TimeType } from '@/types/matrix/douyin'
import { Label } from '@/components/ui/label'
import { Controller, useFormContext } from 'react-hook-form'
import React from 'react'
import { RadioGroup, RadioGroupItem } from '../ui/radio-group'
import { MatrixPublishFormData } from '@/pages/Matrix/publish/VideoPublishMain'
import { Clock, Info } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'

const modeOptions = [
  { value: PublishMode.CHECK_IN, label: '打卡' },
  { value: PublishMode.GOODS, label: '带货' }
]

const settingOptions = [
  { value: Setting.ONE_ACCOUNT_ONE_VIDEO, label: '一个账号一个视频' },
  { value: Setting.ONE_ACCOUNT_MULTIPLE_VIDEOS, label: '一个账号多个视频' }
]

const timeTypeOptions = [
  { value: TimeType.IMMEDIATE, label: '立即发布' },
  { value: TimeType.SCHEDULED, label: '定时发布' },
  { value: TimeType.LOOP, label: '循环定时发布' }
]

export const PublishConfigSections = () => {
  const { control, formState: { errors } } = useFormContext<MatrixPublishFormData>()
 
  return (
    <div className="space-y-6">
      {/* 发布模式 */}
      <div className="flex gap-6 items-center">
        <Label>发布模式</Label>
        <Controller
          name="publishMode"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {modeOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`publishMode-${option.value}`}
                  />
                  <Label
                    htmlFor={`publishMode-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
        />
        {errors?.publishMode && (
          <p className="text-sm text-red-500">{(errors.publishMode as any).message}</p>
        )}
      </div>

      {/* 发布设置 */}
      <div className="flex gap-6 items-center">
        <Label>发布设置</Label>
        <Controller
          name="setting"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {settingOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`setting-${option.value}`}
                  />
                  <Label
                    htmlFor={`setting-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}
        />
        {errors?.setting && (
          <p className="text-sm text-red-500">{(errors.setting as any).message}</p>
        )}
      </div>
      
      {/* 发布时间类型 */}
      <div className="flex gap-6 items-center">
        <Label className="flex items-center gap-2">
          <Clock className="w-4 h-4" />
          发布时间类型
        </Label>
        <Controller
          name="timeType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              value={field.value.toString()}
              onValueChange={(value: string) => field.onChange(Number(value))}
              className="flex gap-3"
            >
              {timeTypeOptions.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={option.value.toString()}
                    id={`timeType-${option.value}`}
                  />
                  <Label
                    htmlFor={`timeType-${option.value}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {option.label}
                  </Label>
                  {option.value === TimeType.LOOP && (
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-2">
                          <h4 className="font-medium">循环定时发布限制</h4>
                          <p className="text-sm text-muted-foreground">
                            • 最大支持14天内日期选择<br />
                            • 最多可选择14天<br />
                            • 超出范围的日期将无法选择
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
              ))}
            </RadioGroup>
          )}
        />
      </div>
    </div>
  )
}
