import request from '../request'
import { PaginatedResult, PaginationParams } from '@app/shared/types/database.types'
import { Mixcut } from '@/types/mixcut'

export const EditorModule = {
  saveEditorState(scriptId: string, data: any) {
    const CLIENT_ID = 'CLIPNEST'
    return request.post<boolean>(
      `/app-api/creative/editor/save?scriptId=${scriptId}&clientId=${CLIENT_ID}`,
      data
    )
  },
  loadEditorState(scriptId: string) {
    return request.get<any>(
      `/app-api/creative/editor/info?scriptId=${scriptId}`,
      undefined,
      { showErrorMessage: false, retryCount: 0 }
    )
  },
  listMixcuts(scriptId: string, params?: PaginationParams) {
    return request.get<PaginatedResult<Mixcut.SavedMixcut>>('/app-api/creative/remix/preview/page', {
      scriptId,
      ...params
    })
  },
  deleteSavedMixcut(data: { ids: string[] }) {
    return request.post('/app-api/creative/remix/preview/delete', data)
  },

  /**
   * 请求渲染
   * @return 渲染任务 ID
   */
  requestRender(data: Mixcut.RequestRender) {
    return request.post<string>('/app-api/creative/render/create', data)
  },
  listRender(list: string[]) {
    return request.post<Mixcut.RenderProgress[]>('/app-api/creative/render/list', {
      taskNos: list
    })
  }
}
